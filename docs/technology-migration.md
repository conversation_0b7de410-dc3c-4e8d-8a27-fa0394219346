# Technology Migration Guide

## Overview
This document outlines the migration from Apache Pulsar to Apache RocketMQ and from Spring Data JPA to MyBatis-Plus in the Enterprise Microservices Framework.

## Migration Summary

### 1. Message Queue Migration: Apache Pulsar → Apache RocketMQ

#### Why RocketMQ?
- **Better Performance**: Lower latency and higher throughput for high-frequency messaging
- **Simplified Operations**: Easier cluster management and monitoring
- **Rich Features**: Built-in message filtering, transaction support, and delay messages
- **Strong Consistency**: Better guarantees for message ordering and delivery

#### Changes Made

##### Dependencies Updated
```xml
<!-- Removed Pulsar -->
<!-- <dependency>
    <groupId>org.apache.pulsar</groupId>
    <artifactId>pulsar-client</artifactId>
    <version>3.1.0</version>
</dependency> -->

<!-- Added RocketMQ -->
<dependency>
    <groupId>org.apache.rocketmq</groupId>
    <artifactId>rocketmq-spring-boot-starter</artifactId>
    <version>2.2.3</version>
</dependency>
<dependency>
    <groupId>org.apache.rocketmq</groupId>
    <artifactId>rocketmq-client</artifactId>
    <version>5.1.4</version>
</dependency>
```

##### Configuration Changes
```yaml
# Old Pulsar Configuration
# pulsar:
#   service-url: pulsar://localhost:6650
#   topics:
#     user-events: user-events

# New RocketMQ Configuration
rocketmq:
  name-server: localhost:9876
  producer:
    group: user-service-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    max-message-size: 4194304
  topics:
    user-events: user-events
```

##### Event Publisher Migration
- **PulsarConfig.java** → **RocketMQConfig.java**
- **UserEventPublisher** updated to use RocketMQ producers
- Message publishing now uses RocketMQ Message objects with properties

### 2. ORM Migration: Spring Data JPA → MyBatis-Plus

#### Why MyBatis-Plus?
- **Better Performance**: Direct SQL control with optimized queries
- **Flexibility**: Custom SQL queries and complex joins
- **Less Magic**: More explicit database operations
- **Better Caching**: Fine-grained cache control
- **Pagination**: Built-in pagination support

#### Changes Made

##### Dependencies Updated
```xml
<!-- Removed JPA -->
<!-- <dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-jpa</artifactId>
</dependency> -->

<!-- Added MyBatis-Plus -->
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-boot-starter</artifactId>
    <version>3.5.3.2</version>
</dependency>
<dependency>
    <groupId>com.baomidou</groupId>
    <artifactId>mybatis-plus-generator</artifactId>
    <version>3.5.3.2</version>
</dependency>
```

##### Entity Annotations Migration
```java
// Old JPA Annotations
@Entity
@Table(name = "users")
@EntityListeners(AuditingEntityListener.class)
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false, length = 50)
    private String username;
    
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
}

// New MyBatis-Plus Annotations
@TableName("users")
public class User {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    @TableField("username")
    private String username;
    
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
```

##### Repository Migration
```java
// Old JPA Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    boolean existsByUsername(String username);
    Page<User> findByStatus(UserStatus status, Pageable pageable);
}

// New MyBatis-Plus Mapper
@Mapper
public interface UserDataMapper extends BaseMapper<User> {
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(@Param("username") String username);
    
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username}")
    boolean existsByUsername(@Param("username") String username);
    
    @Select("SELECT * FROM users WHERE status = #{status}")
    IPage<User> findByStatus(Page<User> page, @Param("status") UserStatus status);
}
```

##### Service Layer Updates
```java
// Old JPA Service Methods
User user = userRepository.findById(id)
    .orElseThrow(() -> new UserNotFoundException("User not found"));
User savedUser = userRepository.save(user);
Page<User> users = userRepository.findAll(pageable);

// New MyBatis-Plus Service Methods
User user = userDataMapper.selectById(id);
if (user == null) {
    throw new UserNotFoundException("User not found");
}
userDataMapper.insert(user);
IPage<User> users = userDataMapper.selectPage(new Page<>(page, size), null);
```

## Infrastructure Changes

### Docker Compose Updates
- Replaced Pulsar services with RocketMQ NameServer and Broker
- Added RocketMQ Console for management
- Updated service dependencies and networking

### Kubernetes Manifests
- Created new RocketMQ cluster manifests
- Updated service configurations for RocketMQ integration
- Modified application configurations for MyBatis-Plus

### Configuration Files
- **broker.conf**: RocketMQ broker configuration
- **MyBatisPlusConfig.java**: Pagination and optimistic locking setup
- **application.yml**: Updated for MyBatis-Plus and RocketMQ

## Migration Benefits

### Performance Improvements
1. **Messaging**: RocketMQ provides better throughput and lower latency
2. **Database**: MyBatis-Plus offers more efficient SQL execution
3. **Memory**: Reduced memory footprint with direct SQL control

### Operational Benefits
1. **Monitoring**: Better observability with RocketMQ Console
2. **Debugging**: Easier to debug with explicit SQL queries
3. **Maintenance**: Simplified cluster management

### Development Benefits
1. **Flexibility**: More control over database operations
2. **Performance Tuning**: Fine-grained optimization capabilities
3. **Testing**: Easier to test with explicit SQL

## Migration Checklist

### Pre-Migration
- [ ] Backup existing data
- [ ] Test new configurations in development
- [ ] Update CI/CD pipelines
- [ ] Train development team

### During Migration
- [ ] Update dependencies in all services
- [ ] Convert entity annotations
- [ ] Migrate repository interfaces
- [ ] Update service layer methods
- [ ] Test all CRUD operations
- [ ] Verify event publishing

### Post-Migration
- [ ] Monitor performance metrics
- [ ] Validate data consistency
- [ ] Update documentation
- [ ] Conduct load testing

## Rollback Plan

### If Issues Occur
1. **Immediate**: Switch traffic back to old version
2. **Database**: Restore from backup if needed
3. **Configuration**: Revert to previous configurations
4. **Dependencies**: Rollback to previous versions

### Monitoring Points
- Message delivery rates
- Database query performance
- Error rates and exceptions
- Memory and CPU usage

## Best Practices

### MyBatis-Plus
1. Use `@TableField(exist = false)` for non-persistent fields
2. Implement proper pagination with `IPage<T>`
3. Use optimistic locking with `@Version`
4. Configure automatic field filling for audit fields

### RocketMQ
1. Use appropriate producer groups for different services
2. Implement proper error handling and retry logic
3. Monitor message accumulation and consumer lag
4. Use tags for message filtering when needed

## Conclusion

The migration from Pulsar to RocketMQ and JPA to MyBatis-Plus provides:
- **Better Performance**: Improved throughput and lower latency
- **More Control**: Fine-grained control over database and messaging operations
- **Easier Operations**: Simplified monitoring and troubleshooting
- **Maintained Architecture**: All DDD principles and patterns preserved

The framework maintains its enterprise-grade capabilities while gaining performance and operational benefits.
