# Deployment Guide

## Overview
This guide provides comprehensive instructions for deploying the Enterprise Microservices Framework in both local development and production environments.

## Prerequisites

### Local Development
- Docker 20.10+
- Docker Compose 2.0+
- 16GB RAM minimum
- 50GB free disk space

### Production (Kubernetes)
- Kubernetes 1.25+
- kubectl configured
- Helm 3.0+
- Persistent storage provisioner
- Load balancer (MetalLB, AWS ALB, etc.)

## Local Development Deployment

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd microservices-framework

# Make scripts executable
chmod +x scripts/*.sh

# Start the complete environment
./scripts/start-local-environment.sh
```

### Step-by-Step Setup

#### 1. Infrastructure Services
```bash
# Start only infrastructure (MySQL, Redis, ZooKeeper)
./scripts/start-local-environment.sh infrastructure

# Verify infrastructure health
docker-compose -f docker/docker-compose.infrastructure.yml ps
```

#### 2. Application Services
```bash
# Start application services (Nacos, Pulsar, APISIX, Monitoring)
./scripts/start-local-environment.sh services

# Check service logs
docker-compose -f docker/docker-compose.services.yml logs -f nacos-1
```

#### 3. Build and Deploy Microservices
```bash
# Build all services
./gradlew build

# Build Docker images
docker build -t enterprise/user-service:1.0.0 services/user-domain/user-service/
docker build -t enterprise/order-service:1.0.0 services/order-domain/order-service/
docker build -t enterprise/product-service:1.0.0 services/product-domain/product-service/
docker build -t enterprise/notification-service:1.0.0 services/notification-domain/notification-service/

# Deploy services
docker-compose -f docker/docker-compose.microservices.yml up -d
```

### Service URLs (Local)
- **APISIX Gateway**: http://localhost:9080
- **Nacos Console**: http://localhost:8848/nacos (nacos/nacos)
- **Sentinel Dashboard**: http://localhost:8858
- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3000 (admin/admin)

## Production Deployment (Kubernetes)

### Prerequisites Setup
```bash
# Create namespaces
kubectl apply -f deployment/kubernetes/namespace.yaml

# Create storage classes (example for AWS EKS)
kubectl apply -f deployment/kubernetes/storage-class.yaml
```

### 1. Infrastructure Deployment

#### MySQL Cluster
```bash
# Deploy MySQL master-slave cluster
kubectl apply -f deployment/kubernetes/mysql-cluster.yaml

# Verify MySQL deployment
kubectl get pods -n microservices-infrastructure -l app=mysql
kubectl get pvc -n microservices-infrastructure
```

#### Redis Cluster
```bash
# Deploy Redis cluster
kubectl apply -f deployment/kubernetes/redis-cluster.yaml

# Wait for Redis pods to be ready
kubectl wait --for=condition=ready pod -l app=redis-cluster -n microservices-infrastructure --timeout=300s

# Initialize Redis cluster
kubectl get job redis-cluster-init -n microservices-infrastructure
```

#### Nacos Cluster
```bash
# Deploy Nacos cluster
kubectl apply -f deployment/kubernetes/nacos-cluster.yaml

# Check Nacos cluster status
kubectl get pods -n microservices-infrastructure -l app=nacos
```

#### Pulsar Cluster
```bash
# Deploy ZooKeeper for Pulsar
kubectl apply -f deployment/kubernetes/zookeeper-cluster.yaml

# Deploy BookKeeper
kubectl apply -f deployment/kubernetes/bookkeeper-cluster.yaml

# Deploy Pulsar brokers
kubectl apply -f deployment/kubernetes/pulsar-cluster.yaml
```

#### APISIX Gateway
```bash
# Deploy etcd for APISIX
kubectl apply -f deployment/kubernetes/etcd-cluster.yaml

# Deploy APISIX
kubectl apply -f deployment/kubernetes/apisix-cluster.yaml
```

### 2. Microservices Deployment

#### User Service
```bash
# Deploy User Service
kubectl apply -f deployment/kubernetes/user-service.yaml

# Check deployment status
kubectl get pods -n microservices -l app=user-service
kubectl get hpa -n microservices user-service-hpa
```

#### Order Service
```bash
# Deploy Order Service
kubectl apply -f deployment/kubernetes/order-service.yaml

# Verify deployment
kubectl rollout status deployment/order-service -n microservices
```

#### Product Service
```bash
# Deploy Product Service
kubectl apply -f deployment/kubernetes/product-service.yaml
```

#### Notification Service
```bash
# Deploy Notification Service
kubectl apply -f deployment/kubernetes/notification-service.yaml
```

### 3. Monitoring and Observability
```bash
# Deploy Prometheus
kubectl apply -f deployment/kubernetes/prometheus.yaml

# Deploy Grafana
kubectl apply -f deployment/kubernetes/grafana.yaml

# Deploy Jaeger for distributed tracing
kubectl apply -f deployment/kubernetes/jaeger.yaml
```

### 4. Ingress and Load Balancing
```bash
# Deploy ingress controller (NGINX example)
kubectl apply -f deployment/kubernetes/ingress-nginx.yaml

# Configure ingress rules
kubectl apply -f deployment/kubernetes/ingress-rules.yaml
```

## Configuration Management

### Environment-Specific Configurations

#### Development
```yaml
# config/dev/application.yml
spring:
  profiles:
    active: dev
  datasource:
    url: ****************************************
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
```

#### Staging
```yaml
# config/staging/application.yml
spring:
  profiles:
    active: staging
  datasource:
    url: ********************************************
  cloud:
    nacos:
      discovery:
        server-addr: nacos-staging:8848
```

#### Production
```yaml
# config/prod/application.yml
spring:
  profiles:
    active: production
  datasource:
    url: ********************************************
  cloud:
    nacos:
      discovery:
        server-addr: nacos-cluster:8848
```

## Scaling and Performance

### Horizontal Pod Autoscaling
```bash
# Check HPA status
kubectl get hpa -n microservices

# Manual scaling
kubectl scale deployment user-service --replicas=5 -n microservices
```

### Vertical Pod Autoscaling
```bash
# Install VPA (if not already installed)
kubectl apply -f https://github.com/kubernetes/autoscaler/releases/download/vertical-pod-autoscaler-0.13.0/vpa-release-0.13.0.yaml

# Apply VPA to services
kubectl apply -f deployment/kubernetes/vpa-config.yaml
```

### Database Scaling
```bash
# Scale MySQL read replicas
kubectl scale statefulset mysql-slave --replicas=3 -n microservices-infrastructure

# Scale Redis cluster (requires manual resharding)
kubectl scale statefulset redis-cluster --replicas=9 -n microservices-infrastructure
```

## Backup and Recovery

### Database Backup
```bash
# MySQL backup
kubectl exec mysql-master-0 -n microservices-infrastructure -- mysqldump -uroot -p$MYSQL_ROOT_PASSWORD --all-databases > backup.sql

# Redis backup
kubectl exec redis-cluster-0 -n microservices-infrastructure -- redis-cli BGSAVE
```

### Configuration Backup
```bash
# Backup Nacos configurations
kubectl exec nacos-0 -n microservices-infrastructure -- curl -X GET "http://localhost:8848/nacos/v1/cs/configs?export=true&group=DEFAULT_GROUP"
```

## Troubleshooting

### Common Issues

#### Service Discovery Issues
```bash
# Check Nacos registration
kubectl logs -n microservices-infrastructure nacos-0

# Verify service registration
curl "http://nacos-url:8848/nacos/v1/ns/instance/list?serviceName=user-service"
```

#### Database Connection Issues
```bash
# Check MySQL connectivity
kubectl exec -it user-service-0 -n microservices -- nc -zv mysql-master 3306

# Check database credentials
kubectl get secret user-service-secret -n microservices -o yaml
```

#### Redis Cluster Issues
```bash
# Check Redis cluster status
kubectl exec redis-cluster-0 -n microservices-infrastructure -- redis-cli cluster nodes

# Fix cluster if needed
kubectl exec redis-cluster-0 -n microservices-infrastructure -- redis-cli cluster fix
```

### Monitoring and Alerts
```bash
# Check Prometheus targets
curl http://prometheus-url:9090/api/v1/targets

# View Grafana dashboards
# Access Grafana UI and import dashboards from deployment/kubernetes/grafana/dashboards/
```

## Security Considerations

### Network Policies
```bash
# Apply network policies
kubectl apply -f deployment/kubernetes/network-policies.yaml
```

### RBAC Configuration
```bash
# Apply RBAC rules
kubectl apply -f deployment/kubernetes/rbac.yaml
```

### Secret Management
```bash
# Use external secret management (e.g., AWS Secrets Manager)
kubectl apply -f deployment/kubernetes/external-secrets.yaml
```

## Maintenance

### Rolling Updates
```bash
# Update service image
kubectl set image deployment/user-service user-service=enterprise/user-service:1.1.0 -n microservices

# Monitor rollout
kubectl rollout status deployment/user-service -n microservices
```

### Database Maintenance
```bash
# MySQL maintenance
kubectl exec mysql-master-0 -n microservices-infrastructure -- mysql -uroot -p$MYSQL_ROOT_PASSWORD -e "OPTIMIZE TABLE user_service.users;"
```

### Log Rotation
```bash
# Configure log rotation for containers
kubectl apply -f deployment/kubernetes/log-rotation.yaml
```
