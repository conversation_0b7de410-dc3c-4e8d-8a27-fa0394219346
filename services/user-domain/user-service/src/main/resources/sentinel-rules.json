{"flowRules": [{"resource": "createUser", "grade": 1, "count": 100, "strategy": 0, "controlBehavior": 0, "clusterMode": false}, {"resource": "getUserById", "grade": 1, "count": 500, "strategy": 0, "controlBehavior": 0, "clusterMode": false}, {"resource": "getUserByUsername", "grade": 1, "count": 300, "strategy": 0, "controlBehavior": 0, "clusterMode": false}, {"resource": "updateUser", "grade": 1, "count": 200, "strategy": 0, "controlBehavior": 0, "clusterMode": false}, {"resource": "userService.createUser", "grade": 1, "count": 50, "strategy": 0, "controlBehavior": 0, "clusterMode": false}], "degradeRules": [{"resource": "userService.createUser", "grade": 2, "count": 0.5, "timeWindow": 10, "minRequestAmount": 5, "slowRatioThreshold": 0.5, "statIntervalMs": 1000}, {"resource": "userService.getUserById", "grade": 0, "count": 50, "timeWindow": 10, "minRequestAmount": 5, "statIntervalMs": 1000}, {"resource": "userService.updateUser", "grade": 1, "count": 10, "timeWindow": 10, "minRequestAmount": 5, "statIntervalMs": 1000}], "systemRules": [{"highestSystemLoad": 10.0, "avgRt": 1000, "maxThread": 100, "qps": 1000, "highestCpuUsage": 0.8}], "authorityRules": [], "paramFlowRules": [{"resource": "getUserById", "count": 10, "grade": 1, "paramIdx": 0, "controlBehavior": 0, "maxQueueingTimeMs": 0, "burstCount": 0, "durationInSec": 1}]}