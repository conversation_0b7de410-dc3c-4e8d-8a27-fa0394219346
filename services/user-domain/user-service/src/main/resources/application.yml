server:
  port: 8081
  servlet:
    context-path: /user-service

spring:
  application:
    name: user-service
  profiles:
    active: dev
  
  # Database Configuration
  datasource:
    url: *****************************************************************************************************
    username: ${DB_USERNAME:user_service}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  # MyBatis-Plus Configuration
  mybatis-plus:
    configuration:
      map-underscore-to-camel-case: true
      cache-enabled: false
      log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
    global-config:
      db-config:
        id-type: auto
        logic-delete-field: deleted
        logic-delete-value: 1
        logic-not-delete-value: 0
      banner: false
    mapper-locations: classpath*:/mapper/**/*.xml
    type-aliases-package: com.enterprise.microservices.user.domain
  
  # Redis Configuration
  data:
    redis:
      host: localhost
      port: 6379
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # Cache Configuration
  cache:
    type: redis
    redis:
      time-to-live: 600000 # 10 minutes
      cache-null-values: false
  
  # Security Configuration
  security:
    user:
      name: admin
      password: admin123

# Nacos Configuration
spring:
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: ${NACOS_NAMESPACE:public}
        group: ${NACOS_GROUP:DEFAULT_GROUP}
        cluster-name: ${NACOS_CLUSTER:default}
        metadata:
          version: 1.0.0
          region: ${REGION:us-east-1}
          zone: ${ZONE:us-east-1a}
      config:
        server-addr: localhost:8848
        file-extension: yml
        namespace: ${NACOS_NAMESPACE:public}
        group: ${NACOS_GROUP:DEFAULT_GROUP}

# Sentinel Configuration
spring:
  cloud:
    sentinel:
      transport:
        dashboard: localhost:8080
        port: 8719
      datasource:
        flow:
          nacos:
            server-addr: localhost:8848
            dataId: user-service-flow-rules
            groupId: SENTINEL_GROUP
            rule-type: flow
        degrade:
          nacos:
            server-addr: localhost:8848
            dataId: user-service-degrade-rules
            groupId: SENTINEL_GROUP
            rule-type: degrade

# RocketMQ Configuration
rocketmq:
  name-server: localhost:9876
  producer:
    group: user-service-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    max-message-size: 4194304
  topics:
    user-events: user-events

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# Logging Configuration
logging:
  level:
    com.enterprise.microservices.user: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/user-service.log

# Custom Application Properties
app:
  jwt:
    secret: ${JWT_SECRET:mySecretKey}
    expiration: 86400000 # 24 hours
  
  security:
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special-char: false
