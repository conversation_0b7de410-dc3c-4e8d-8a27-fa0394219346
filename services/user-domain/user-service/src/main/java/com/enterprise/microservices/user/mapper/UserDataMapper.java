package com.enterprise.microservices.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.enterprise.microservices.user.domain.User;
import com.enterprise.microservices.user.domain.UserStatus;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * User Data Mapper
 * MyBatis-Plus mapper interface for User entity data access
 */
@Mapper
public interface UserDataMapper extends BaseMapper<User> {

    /**
     * Find user by username
     */
    @Select("SELECT * FROM users WHERE username = #{username}")
    User findByUsername(@Param("username") String username);

    /**
     * Find user by email
     */
    @Select("SELECT * FROM users WHERE email = #{email}")
    User findByEmail(@Param("email") String email);

    /**
     * Check if username exists
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE username = #{username}")
    boolean existsByUsername(@Param("username") String username);

    /**
     * Check if email exists
     */
    @Select("SELECT COUNT(*) > 0 FROM users WHERE email = #{email}")
    boolean existsByEmail(@Param("email") String email);

    /**
     * Find users by status with pagination
     */
    @Select("SELECT * FROM users WHERE status = #{status}")
    IPage<User> findByStatus(Page<User> page, @Param("status") UserStatus status);

    /**
     * Find users by email verified status with pagination
     */
    @Select("SELECT * FROM users WHERE email_verified = #{emailVerified}")
    IPage<User> findByEmailVerified(Page<User> page, @Param("emailVerified") Boolean emailVerified);

    /**
     * Find users created after a specific date
     */
    @Select("SELECT * FROM users WHERE created_at >= #{date}")
    List<User> findUsersCreatedAfter(@Param("date") LocalDateTime date);

    /**
     * Find users who haven't logged in for a specific period
     */
    @Select("SELECT * FROM users WHERE last_login_at < #{date} OR last_login_at IS NULL")
    List<User> findInactiveUsers(@Param("date") LocalDateTime date);

    /**
     * Count users by status
     */
    @Select("SELECT COUNT(*) FROM users WHERE status = #{status}")
    long countByStatus(@Param("status") UserStatus status);

    /**
     * Find users by partial username match with pagination
     */
    @Select("SELECT * FROM users WHERE username LIKE CONCAT('%', #{username}, '%')")
    IPage<User> findByUsernameContaining(Page<User> page, @Param("username") String username);

    /**
     * Find users by partial email match with pagination
     */
    @Select("SELECT * FROM users WHERE email LIKE CONCAT('%', #{email}, '%')")
    IPage<User> findByEmailContaining(Page<User> page, @Param("email") String email);

    /**
     * Find users by full name with pagination
     */
    @Select("SELECT * FROM users WHERE CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) LIKE CONCAT('%', #{fullName}, '%')")
    IPage<User> findByFullNameContaining(Page<User> page, @Param("fullName") String fullName);

    /**
     * Update user last login time
     */
    @Update("UPDATE users SET last_login_at = #{lastLoginAt}, updated_at = NOW() WHERE id = #{id}")
    int updateLastLoginAt(@Param("id") Long id, @Param("lastLoginAt") LocalDateTime lastLoginAt);

    /**
     * Update user status
     */
    @Update("UPDATE users SET status = #{status}, updated_at = NOW() WHERE id = #{id}")
    int updateUserStatus(@Param("id") Long id, @Param("status") UserStatus status);

    /**
     * Verify user email
     */
    @Update("UPDATE users SET email_verified = true, updated_at = NOW() WHERE id = #{id}")
    int verifyEmail(@Param("id") Long id);

    /**
     * Verify user phone
     */
    @Update("UPDATE users SET phone_verified = true, updated_at = NOW() WHERE id = #{id}")
    int verifyPhone(@Param("id") Long id);

    /**
     * Find users with complex conditions
     */
    @Select("<script>" +
            "SELECT * FROM users WHERE 1=1" +
            "<if test='status != null'> AND status = #{status}</if>" +
            "<if test='emailVerified != null'> AND email_verified = #{emailVerified}</if>" +
            "<if test='phoneVerified != null'> AND phone_verified = #{phoneVerified}</if>" +
            "<if test='createdAfter != null'> AND created_at >= #{createdAfter}</if>" +
            "<if test='createdBefore != null'> AND created_at &lt;= #{createdBefore}</if>" +
            " ORDER BY created_at DESC" +
            "</script>")
    IPage<User> findUsersWithConditions(
            Page<User> page,
            @Param("status") UserStatus status,
            @Param("emailVerified") Boolean emailVerified,
            @Param("phoneVerified") Boolean phoneVerified,
            @Param("createdAfter") LocalDateTime createdAfter,
            @Param("createdBefore") LocalDateTime createdBefore
    );
}
