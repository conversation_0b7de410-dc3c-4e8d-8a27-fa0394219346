package com.enterprise.microservices.user.service;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.enterprise.microservices.user.domain.User;
import com.enterprise.microservices.user.dto.CreateUserRequest;
import com.enterprise.microservices.user.dto.UpdateUserRequest;
import com.enterprise.microservices.user.dto.UserResponse;
import com.enterprise.microservices.user.event.UserEvent;
import com.enterprise.microservices.user.exception.UserAlreadyExistsException;
import com.enterprise.microservices.user.exception.UserNotFoundException;
import com.enterprise.microservices.user.mapper.UserMapper;
import com.enterprise.microservices.user.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * User Service
 * Business logic for user management operations
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private UserEventPublisher eventPublisher;

    /**
     * Create a new user
     */
    @SentinelResource(value = "userService.createUser", 
                     fallback = "createUserFallback",
                     blockHandler = "createUserBlockHandler")
    public UserResponse createUser(CreateUserRequest request) {
        logger.info("Creating user with username: {}", request.getUsername());

        // Check if user already exists
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new UserAlreadyExistsException("Username already exists: " + request.getUsername());
        }

        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException("Email already exists: " + request.getEmail());
        }

        // Create user entity
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPhoneNumber(request.getPhoneNumber());

        // Save user
        User savedUser = userRepository.save(user);

        // Publish domain event
        UserEvent.UserRegistered event = new UserEvent.UserRegistered(
            savedUser.getId(),
            savedUser.getUsername(),
            savedUser.getEmail(),
            savedUser.getCreatedAt()
        );
        eventPublisher.publishUserRegistered(event);

        logger.info("User created successfully with ID: {}", savedUser.getId());
        return userMapper.toResponse(savedUser);
    }

    /**
     * Get user by ID
     */
    @Cacheable(value = "users", key = "#id")
    @SentinelResource(value = "userService.getUserById",
                     fallback = "getUserByIdFallback")
    @Transactional(readOnly = true)
    public UserResponse getUserById(Long id) {
        logger.debug("Fetching user with ID: {}", id);

        User user = userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));

        return userMapper.toResponse(user);
    }

    /**
     * Get user by username
     */
    @Cacheable(value = "users", key = "#username")
    @SentinelResource(value = "userService.getUserByUsername",
                     fallback = "getUserByUsernameFallback")
    @Transactional(readOnly = true)
    public UserResponse getUserByUsername(String username) {
        logger.debug("Fetching user with username: {}", username);

        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new UserNotFoundException("User not found with username: " + username));

        return userMapper.toResponse(user);
    }

    /**
     * Get user by email
     */
    @Cacheable(value = "users", key = "#email")
    @SentinelResource(value = "userService.getUserByEmail",
                     fallback = "getUserByEmailFallback")
    @Transactional(readOnly = true)
    public UserResponse getUserByEmail(String email) {
        logger.debug("Fetching user with email: {}", email);

        User user = userRepository.findByEmail(email)
            .orElseThrow(() -> new UserNotFoundException("User not found with email: " + email));

        return userMapper.toResponse(user);
    }

    /**
     * Update user
     */
    @CacheEvict(value = "users", allEntries = true)
    @SentinelResource(value = "userService.updateUser",
                     fallback = "updateUserFallback")
    public UserResponse updateUser(Long id, UpdateUserRequest request) {
        logger.info("Updating user with ID: {}", id);

        User user = userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));

        // Update fields
        if (request.getFirstName() != null) {
            user.setFirstName(request.getFirstName());
        }
        if (request.getLastName() != null) {
            user.setLastName(request.getLastName());
        }
        if (request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
        }

        User updatedUser = userRepository.save(user);

        // Publish domain event
        UserEvent.UserUpdated event = new UserEvent.UserUpdated(
            updatedUser.getId(),
            updatedUser.getUsername(),
            updatedUser.getUpdatedAt()
        );
        eventPublisher.publishUserUpdated(event);

        logger.info("User updated successfully with ID: {}", id);
        return userMapper.toResponse(updatedUser);
    }

    /**
     * Activate user
     */
    @CacheEvict(value = "users", allEntries = true)
    @SentinelResource(value = "userService.activateUser")
    public void activateUser(Long id) {
        logger.info("Activating user with ID: {}", id);

        User user = userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));

        user.activate();
        userRepository.save(user);

        // Publish domain event
        UserEvent.UserActivated event = new UserEvent.UserActivated(
            user.getId(),
            user.getUsername(),
            user.getUpdatedAt()
        );
        eventPublisher.publishUserActivated(event);

        logger.info("User activated successfully with ID: {}", id);
    }

    /**
     * Deactivate user
     */
    @CacheEvict(value = "users", allEntries = true)
    @SentinelResource(value = "userService.deactivateUser")
    public void deactivateUser(Long id) {
        logger.info("Deactivating user with ID: {}", id);

        User user = userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));

        user.deactivate();
        userRepository.save(user);

        // Publish domain event
        UserEvent.UserDeactivated event = new UserEvent.UserDeactivated(
            user.getId(),
            user.getUsername(),
            user.getUpdatedAt()
        );
        eventPublisher.publishUserDeactivated(event);

        logger.info("User deactivated successfully with ID: {}", id);
    }

    /**
     * Get all users with pagination
     */
    @SentinelResource(value = "userService.getAllUsers")
    @Transactional(readOnly = true)
    public Page<UserResponse> getAllUsers(Pageable pageable) {
        logger.debug("Fetching users with pagination: {}", pageable);

        Page<User> users = userRepository.findAll(pageable);
        return users.map(userMapper::toResponse);
    }

    /**
     * Delete user
     */
    @CacheEvict(value = "users", allEntries = true)
    @SentinelResource(value = "userService.deleteUser")
    public void deleteUser(Long id) {
        logger.info("Deleting user with ID: {}", id);

        User user = userRepository.findById(id)
            .orElseThrow(() -> new UserNotFoundException("User not found with ID: " + id));

        userRepository.delete(user);

        logger.info("User deleted successfully with ID: {}", id);
    }

    // Fallback methods for circuit breaker
    public UserResponse createUserFallback(CreateUserRequest request, Throwable ex) {
        logger.error("Circuit breaker activated for createUser", ex);
        throw new RuntimeException("User service is temporarily unavailable");
    }

    public UserResponse getUserByIdFallback(Long id, Throwable ex) {
        logger.error("Circuit breaker activated for getUserById", ex);
        throw new RuntimeException("User service is temporarily unavailable");
    }

    public UserResponse getUserByUsernameFallback(String username, Throwable ex) {
        logger.error("Circuit breaker activated for getUserByUsername", ex);
        throw new RuntimeException("User service is temporarily unavailable");
    }

    public UserResponse getUserByEmailFallback(String email, Throwable ex) {
        logger.error("Circuit breaker activated for getUserByEmail", ex);
        throw new RuntimeException("User service is temporarily unavailable");
    }

    public UserResponse updateUserFallback(Long id, UpdateUserRequest request, Throwable ex) {
        logger.error("Circuit breaker activated for updateUser", ex);
        throw new RuntimeException("User service is temporarily unavailable");
    }

    // Block handlers for rate limiting
    public UserResponse createUserBlockHandler(CreateUserRequest request, Exception ex) {
        logger.warn("Rate limit exceeded for createUser");
        throw new RuntimeException("Too many requests, please try again later");
    }
}
