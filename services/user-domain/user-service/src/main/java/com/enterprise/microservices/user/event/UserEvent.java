package com.enterprise.microservices.user.event;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * User Domain Events
 * Events published when user-related actions occur
 */
public class UserEvent {

    /**
     * User Registered Event
     */
    public static class UserRegistered {
        private Long userId;
        private String username;
        private String email;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;

        public UserRegistered() {}

        public UserRegistered(Long userId, String username, String email, LocalDateTime timestamp) {
            this.userId = userId;
            this.username = username;
            this.email = email;
            this.timestamp = timestamp;
        }

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }

        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }

    /**
     * User Updated Event
     */
    public static class UserUpdated {
        private Long userId;
        private String username;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;

        public UserUpdated() {}

        public UserUpdated(Long userId, String username, LocalDateTime timestamp) {
            this.userId = userId;
            this.username = username;
            this.timestamp = timestamp;
        }

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }

    /**
     * User Activated Event
     */
    public static class UserActivated {
        private Long userId;
        private String username;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;

        public UserActivated() {}

        public UserActivated(Long userId, String username, LocalDateTime timestamp) {
            this.userId = userId;
            this.username = username;
            this.timestamp = timestamp;
        }

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }

    /**
     * User Deactivated Event
     */
    public static class UserDeactivated {
        private Long userId;
        private String username;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime timestamp;

        public UserDeactivated() {}

        public UserDeactivated(Long userId, String username, LocalDateTime timestamp) {
            this.userId = userId;
            this.username = username;
            this.timestamp = timestamp;
        }

        // Getters and Setters
        public Long getUserId() { return userId; }
        public void setUserId(Long userId) { this.userId = userId; }

        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    }
}
