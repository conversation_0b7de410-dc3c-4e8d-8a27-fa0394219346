package com.enterprise.microservices.user.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.enterprise.microservices.user.dto.CreateUserRequest;
import com.enterprise.microservices.user.dto.UpdateUserRequest;
import com.enterprise.microservices.user.dto.UserResponse;
import com.enterprise.microservices.user.service.UserService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * User Controller
 * REST API endpoints for user management operations
 */
@RestController
@RequestMapping("/api/v1/users")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    /**
     * Create a new user
     */
    @PostMapping
    @SentinelResource(value = "createUser", blockHandler = "createUserBlockHandler")
    public ResponseEntity<UserResponse> createUser(@Valid @RequestBody CreateUserRequest request) {
        logger.info("Creating user with username: {}", request.getUsername());
        
        UserResponse user = userService.createUser(request);
        
        logger.info("User created successfully with ID: {}", user.getId());
        return ResponseEntity.status(HttpStatus.CREATED).body(user);
    }

    /**
     * Get user by ID
     */
    @GetMapping("/{id}")
    @SentinelResource(value = "getUserById", blockHandler = "getUserByIdBlockHandler")
    public ResponseEntity<UserResponse> getUserById(@PathVariable Long id) {
        logger.info("Fetching user with ID: {}", id);
        
        UserResponse user = userService.getUserById(id);
        
        return ResponseEntity.ok(user);
    }

    /**
     * Get user by username
     */
    @GetMapping("/username/{username}")
    @SentinelResource(value = "getUserByUsername", blockHandler = "getUserByUsernameBlockHandler")
    public ResponseEntity<UserResponse> getUserByUsername(@PathVariable String username) {
        logger.info("Fetching user with username: {}", username);
        
        UserResponse user = userService.getUserByUsername(username);
        
        return ResponseEntity.ok(user);
    }

    /**
     * Get user by email
     */
    @GetMapping("/email/{email}")
    @SentinelResource(value = "getUserByEmail", blockHandler = "getUserByEmailBlockHandler")
    public ResponseEntity<UserResponse> getUserByEmail(@PathVariable String email) {
        logger.info("Fetching user with email: {}", email);
        
        UserResponse user = userService.getUserByEmail(email);
        
        return ResponseEntity.ok(user);
    }

    /**
     * Update user
     */
    @PutMapping("/{id}")
    @SentinelResource(value = "updateUser", blockHandler = "updateUserBlockHandler")
    public ResponseEntity<UserResponse> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UpdateUserRequest request) {
        logger.info("Updating user with ID: {}", id);
        
        UserResponse user = userService.updateUser(id, request);
        
        logger.info("User updated successfully with ID: {}", id);
        return ResponseEntity.ok(user);
    }

    /**
     * Activate user
     */
    @PostMapping("/{id}/activate")
    @SentinelResource(value = "activateUser", blockHandler = "activateUserBlockHandler")
    public ResponseEntity<Void> activateUser(@PathVariable Long id) {
        logger.info("Activating user with ID: {}", id);
        
        userService.activateUser(id);
        
        logger.info("User activated successfully with ID: {}", id);
        return ResponseEntity.ok().build();
    }

    /**
     * Deactivate user
     */
    @PostMapping("/{id}/deactivate")
    @SentinelResource(value = "deactivateUser", blockHandler = "deactivateUserBlockHandler")
    public ResponseEntity<Void> deactivateUser(@PathVariable Long id) {
        logger.info("Deactivating user with ID: {}", id);
        
        userService.deactivateUser(id);
        
        logger.info("User deactivated successfully with ID: {}", id);
        return ResponseEntity.ok().build();
    }

    /**
     * Get all users with pagination
     */
    @GetMapping
    @SentinelResource(value = "getAllUsers", blockHandler = "getAllUsersBlockHandler")
    public ResponseEntity<Page<UserResponse>> getAllUsers(Pageable pageable) {
        logger.info("Fetching users with pagination: {}", pageable);
        
        Page<UserResponse> users = userService.getAllUsers(pageable);
        
        return ResponseEntity.ok(users);
    }

    /**
     * Delete user
     */
    @DeleteMapping("/{id}")
    @SentinelResource(value = "deleteUser", blockHandler = "deleteUserBlockHandler")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        logger.info("Deleting user with ID: {}", id);
        
        userService.deleteUser(id);
        
        logger.info("User deleted successfully with ID: {}", id);
        return ResponseEntity.noContent().build();
    }

    // Sentinel Block Handlers
    public ResponseEntity<UserResponse> createUserBlockHandler(CreateUserRequest request, Exception ex) {
        logger.warn("Rate limit exceeded for createUser operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<UserResponse> getUserByIdBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for getUserById operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<UserResponse> getUserByUsernameBlockHandler(String username, Exception ex) {
        logger.warn("Rate limit exceeded for getUserByUsername operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<UserResponse> getUserByEmailBlockHandler(String email, Exception ex) {
        logger.warn("Rate limit exceeded for getUserByEmail operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<UserResponse> updateUserBlockHandler(Long id, UpdateUserRequest request, Exception ex) {
        logger.warn("Rate limit exceeded for updateUser operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Void> activateUserBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for activateUser operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Void> deactivateUserBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for deactivateUser operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Page<UserResponse>> getAllUsersBlockHandler(Pageable pageable, Exception ex) {
        logger.warn("Rate limit exceeded for getAllUsers operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Void> deleteUserBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for deleteUser operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }
}
