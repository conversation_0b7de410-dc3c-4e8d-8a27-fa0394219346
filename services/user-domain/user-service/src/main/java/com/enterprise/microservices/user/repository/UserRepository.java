package com.enterprise.microservices.user.repository;

import com.enterprise.microservices.user.domain.User;
import com.enterprise.microservices.user.domain.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * User Repository
 * Data access layer for User entity
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * Find user by username
     */
    Optional<User> findByUsername(String username);

    /**
     * Find user by email
     */
    Optional<User> findByEmail(String email);

    /**
     * Check if username exists
     */
    boolean existsByUsername(String username);

    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);

    /**
     * Find users by status
     */
    Page<User> findByStatus(UserStatus status, Pageable pageable);

    /**
     * Find users by email verified status
     */
    Page<User> findByEmailVerified(Boolean emailVerified, Pageable pageable);

    /**
     * Find users created after a specific date
     */
    @Query("SELECT u FROM User u WHERE u.createdAt >= :date")
    List<User> findUsersCreatedAfter(@Param("date") LocalDateTime date);

    /**
     * Find users who haven't logged in for a specific period
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt < :date OR u.lastLoginAt IS NULL")
    List<User> findInactiveUsers(@Param("date") LocalDateTime date);

    /**
     * Count users by status
     */
    long countByStatus(UserStatus status);

    /**
     * Find users by partial username match
     */
    @Query("SELECT u FROM User u WHERE u.username LIKE %:username%")
    Page<User> findByUsernameContaining(@Param("username") String username, Pageable pageable);

    /**
     * Find users by partial email match
     */
    @Query("SELECT u FROM User u WHERE u.email LIKE %:email%")
    Page<User> findByEmailContaining(@Param("email") String email, Pageable pageable);

    /**
     * Find users by full name
     */
    @Query("SELECT u FROM User u WHERE " +
           "CONCAT(COALESCE(u.firstName, ''), ' ', COALESCE(u.lastName, '')) LIKE %:fullName%")
    Page<User> findByFullNameContaining(@Param("fullName") String fullName, Pageable pageable);
}
