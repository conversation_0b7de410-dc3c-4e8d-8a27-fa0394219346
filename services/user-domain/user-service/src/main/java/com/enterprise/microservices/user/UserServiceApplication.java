package com.enterprise.microservices.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cache.annotation.EnableCaching;

/**
 * User Service Application
 * Main entry point for the User Domain microservice
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableCaching
@MapperScan("com.enterprise.microservices.user.mapper")
public class UserServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserServiceApplication.class, args);
    }
}
