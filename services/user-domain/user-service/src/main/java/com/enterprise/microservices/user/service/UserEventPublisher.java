package com.enterprise.microservices.user.service;

import com.enterprise.microservices.user.event.UserEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

/**
 * User Event Publisher
 * Publishes user domain events to Apache Pulsar
 */
@Service
public class UserEventPublisher {

    private static final Logger logger = LoggerFactory.getLogger(UserEventPublisher.class);

    @Autowired
    private PulsarClient pulsarClient;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${pulsar.topics.user-events:user-events}")
    private String userEventsTopic;

    private Producer<String> producer;

    @PostConstruct
    public void init() {
        try {
            producer = pulsarClient.newProducer(org.apache.pulsar.client.api.Schema.STRING)
                    .topic(userEventsTopic)
                    .create();
            logger.info("User event producer initialized for topic: {}", userEventsTopic);
        } catch (PulsarClientException e) {
            logger.error("Failed to initialize user event producer", e);
            throw new RuntimeException("Failed to initialize user event producer", e);
        }
    }

    @PreDestroy
    public void cleanup() {
        if (producer != null) {
            try {
                producer.close();
                logger.info("User event producer closed");
            } catch (PulsarClientException e) {
                logger.error("Error closing user event producer", e);
            }
        }
    }

    /**
     * Publish UserRegistered event
     */
    public void publishUserRegistered(UserEvent.UserRegistered event) {
        publishEvent("UserRegistered", event);
    }

    /**
     * Publish UserUpdated event
     */
    public void publishUserUpdated(UserEvent.UserUpdated event) {
        publishEvent("UserUpdated", event);
    }

    /**
     * Publish UserActivated event
     */
    public void publishUserActivated(UserEvent.UserActivated event) {
        publishEvent("UserActivated", event);
    }

    /**
     * Publish UserDeactivated event
     */
    public void publishUserDeactivated(UserEvent.UserDeactivated event) {
        publishEvent("UserDeactivated", event);
    }

    /**
     * Generic method to publish events
     */
    private void publishEvent(String eventType, Object event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);
            
            producer.newMessage()
                    .key(eventType)
                    .property("eventType", eventType)
                    .property("service", "user-service")
                    .value(eventJson)
                    .sendAsync()
                    .thenAccept(messageId -> {
                        logger.debug("Published {} event with message ID: {}", eventType, messageId);
                    })
                    .exceptionally(throwable -> {
                        logger.error("Failed to publish {} event", eventType, throwable);
                        return null;
                    });
                    
        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize {} event", eventType, e);
        }
    }
}
