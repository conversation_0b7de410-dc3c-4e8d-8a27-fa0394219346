package com.enterprise.microservices.user.service;

import com.enterprise.microservices.user.event.UserEvent;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PreDestroy;

/**
 * User Event Publisher
 * Publishes user domain events to Apache RocketMQ
 */
@Service
public class UserEventPublisher {

    private static final Logger logger = LoggerFactory.getLogger(UserEventPublisher.class);

    @Autowired
    private DefaultMQProducer producer;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${rocketmq.topics.user-events:user-events}")
    private String userEventsTopic;

    @PreDestroy
    public void cleanup() {
        if (producer != null) {
            try {
                producer.shutdown();
                logger.info("User event producer shutdown");
            } catch (Exception e) {
                logger.error("Error shutting down user event producer", e);
            }
        }
    }

    /**
     * Publish UserRegistered event
     */
    public void publishUserRegistered(UserEvent.UserRegistered event) {
        publishEvent("UserRegistered", event);
    }

    /**
     * Publish UserUpdated event
     */
    public void publishUserUpdated(UserEvent.UserUpdated event) {
        publishEvent("UserUpdated", event);
    }

    /**
     * Publish UserActivated event
     */
    public void publishUserActivated(UserEvent.UserActivated event) {
        publishEvent("UserActivated", event);
    }

    /**
     * Publish UserDeactivated event
     */
    public void publishUserDeactivated(UserEvent.UserDeactivated event) {
        publishEvent("UserDeactivated", event);
    }

    /**
     * Generic method to publish events
     */
    private void publishEvent(String eventType, Object event) {
        try {
            String eventJson = objectMapper.writeValueAsString(event);

            Message message = new Message(userEventsTopic, eventType, eventJson.getBytes());
            message.putUserProperty("eventType", eventType);
            message.putUserProperty("service", "user-service");
            message.putUserProperty("timestamp", String.valueOf(System.currentTimeMillis()));

            SendResult sendResult = producer.send(message);
            logger.debug("Published {} event with message ID: {}", eventType, sendResult.getMsgId());

        } catch (JsonProcessingException e) {
            logger.error("Failed to serialize {} event", eventType, e);
        } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
            logger.error("Failed to publish {} event to RocketMQ", eventType, e);
        }
    }
}
