package com.enterprise.microservices.order.controller;

import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.enterprise.microservices.order.dto.CreateOrderRequest;
import com.enterprise.microservices.order.dto.OrderResponse;
import com.enterprise.microservices.order.dto.UpdateOrderRequest;
import com.enterprise.microservices.order.service.OrderService;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Order Controller
 * REST API endpoints for order management operations
 */
@RestController
@RequestMapping("/api/v1/orders")
@CrossOrigin(origins = "*", maxAge = 3600)
public class OrderController {

    private static final Logger logger = LoggerFactory.getLogger(OrderController.class);

    @Autowired
    private OrderService orderService;

    /**
     * Create a new order
     */
    @PostMapping
    @SentinelResource(value = "createOrder", blockHandler = "createOrderBlockHandler")
    public ResponseEntity<OrderResponse> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        logger.info("Creating order for user: {}", request.getUserId());
        
        OrderResponse order = orderService.createOrder(request);
        
        logger.info("Order created successfully with ID: {}", order.getId());
        return ResponseEntity.status(HttpStatus.CREATED).body(order);
    }

    /**
     * Get order by ID
     */
    @GetMapping("/{id}")
    @SentinelResource(value = "getOrderById", blockHandler = "getOrderByIdBlockHandler")
    public ResponseEntity<OrderResponse> getOrderById(@PathVariable Long id) {
        logger.info("Fetching order with ID: {}", id);
        
        OrderResponse order = orderService.getOrderById(id);
        
        return ResponseEntity.ok(order);
    }

    /**
     * Get order by order number
     */
    @GetMapping("/number/{orderNumber}")
    @SentinelResource(value = "getOrderByNumber", blockHandler = "getOrderByNumberBlockHandler")
    public ResponseEntity<OrderResponse> getOrderByNumber(@PathVariable String orderNumber) {
        logger.info("Fetching order with number: {}", orderNumber);
        
        OrderResponse order = orderService.getOrderByNumber(orderNumber);
        
        return ResponseEntity.ok(order);
    }

    /**
     * Get orders by user ID
     */
    @GetMapping("/user/{userId}")
    @SentinelResource(value = "getOrdersByUserId", blockHandler = "getOrdersByUserIdBlockHandler")
    public ResponseEntity<Page<OrderResponse>> getOrdersByUserId(
            @PathVariable Long userId, Pageable pageable) {
        logger.info("Fetching orders for user: {}", userId);
        
        Page<OrderResponse> orders = orderService.getOrdersByUserId(userId, pageable);
        
        return ResponseEntity.ok(orders);
    }

    /**
     * Update order
     */
    @PutMapping("/{id}")
    @SentinelResource(value = "updateOrder", blockHandler = "updateOrderBlockHandler")
    public ResponseEntity<OrderResponse> updateOrder(
            @PathVariable Long id,
            @Valid @RequestBody UpdateOrderRequest request) {
        logger.info("Updating order with ID: {}", id);
        
        OrderResponse order = orderService.updateOrder(id, request);
        
        logger.info("Order updated successfully with ID: {}", id);
        return ResponseEntity.ok(order);
    }

    /**
     * Confirm order
     */
    @PostMapping("/{id}/confirm")
    @SentinelResource(value = "confirmOrder", blockHandler = "confirmOrderBlockHandler")
    public ResponseEntity<Void> confirmOrder(@PathVariable Long id) {
        logger.info("Confirming order with ID: {}", id);
        
        orderService.confirmOrder(id);
        
        logger.info("Order confirmed successfully with ID: {}", id);
        return ResponseEntity.ok().build();
    }

    /**
     * Ship order
     */
    @PostMapping("/{id}/ship")
    @SentinelResource(value = "shipOrder", blockHandler = "shipOrderBlockHandler")
    public ResponseEntity<Void> shipOrder(@PathVariable Long id) {
        logger.info("Shipping order with ID: {}", id);
        
        orderService.shipOrder(id);
        
        logger.info("Order shipped successfully with ID: {}", id);
        return ResponseEntity.ok().build();
    }

    /**
     * Deliver order
     */
    @PostMapping("/{id}/deliver")
    @SentinelResource(value = "deliverOrder", blockHandler = "deliverOrderBlockHandler")
    public ResponseEntity<Void> deliverOrder(@PathVariable Long id) {
        logger.info("Delivering order with ID: {}", id);
        
        orderService.deliverOrder(id);
        
        logger.info("Order delivered successfully with ID: {}", id);
        return ResponseEntity.ok().build();
    }

    /**
     * Cancel order
     */
    @PostMapping("/{id}/cancel")
    @SentinelResource(value = "cancelOrder", blockHandler = "cancelOrderBlockHandler")
    public ResponseEntity<Void> cancelOrder(@PathVariable Long id) {
        logger.info("Cancelling order with ID: {}", id);
        
        orderService.cancelOrder(id);
        
        logger.info("Order cancelled successfully with ID: {}", id);
        return ResponseEntity.ok().build();
    }

    /**
     * Get all orders with pagination
     */
    @GetMapping
    @SentinelResource(value = "getAllOrders", blockHandler = "getAllOrdersBlockHandler")
    public ResponseEntity<Page<OrderResponse>> getAllOrders(Pageable pageable) {
        logger.info("Fetching orders with pagination: {}", pageable);
        
        Page<OrderResponse> orders = orderService.getAllOrders(pageable);
        
        return ResponseEntity.ok(orders);
    }

    // Sentinel Block Handlers
    public ResponseEntity<OrderResponse> createOrderBlockHandler(CreateOrderRequest request, Exception ex) {
        logger.warn("Rate limit exceeded for createOrder operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<OrderResponse> getOrderByIdBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for getOrderById operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<OrderResponse> getOrderByNumberBlockHandler(String orderNumber, Exception ex) {
        logger.warn("Rate limit exceeded for getOrderByNumber operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Page<OrderResponse>> getOrdersByUserIdBlockHandler(Long userId, Pageable pageable, Exception ex) {
        logger.warn("Rate limit exceeded for getOrdersByUserId operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<OrderResponse> updateOrderBlockHandler(Long id, UpdateOrderRequest request, Exception ex) {
        logger.warn("Rate limit exceeded for updateOrder operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Void> confirmOrderBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for confirmOrder operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Void> shipOrderBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for shipOrder operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Void> deliverOrderBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for deliverOrder operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Void> cancelOrderBlockHandler(Long id, Exception ex) {
        logger.warn("Rate limit exceeded for cancelOrder operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }

    public ResponseEntity<Page<OrderResponse>> getAllOrdersBlockHandler(Pageable pageable, Exception ex) {
        logger.warn("Rate limit exceeded for getAllOrders operation");
        return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
    }
}
