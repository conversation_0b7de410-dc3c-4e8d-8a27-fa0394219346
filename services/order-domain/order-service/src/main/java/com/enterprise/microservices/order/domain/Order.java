package com.enterprise.microservices.order.domain;

import com.baomidou.mybatisplus.annotation.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Order Aggregate Root
 * Represents the core order entity in the Order bounded context
 */
@TableName("orders")
public class Order {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    @NotNull(message = "User ID is required")
    private Long userId;

    @TableField("order_number")
    private String orderNumber;

    @TableField("status")
    private OrderStatus status = OrderStatus.PENDING;

    @TableField("total_amount")
    @NotNull(message = "Total amount is required")
    @Positive(message = "Total amount must be positive")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    @TableField("tax_amount")
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @TableField("shipping_amount")
    private BigDecimal shippingAmount = BigDecimal.ZERO;

    @TableField("discount_amount")
    private BigDecimal discountAmount = BigDecimal.ZERO;

    @TableField("notes")
    private String notes;

    // Shipping address fields (flattened)
    @TableField("shipping_first_name")
    private String shippingFirstName;

    @TableField("shipping_last_name")
    private String shippingLastName;

    @TableField("shipping_address_line1")
    private String shippingAddressLine1;

    @TableField("shipping_address_line2")
    private String shippingAddressLine2;

    @TableField("shipping_city")
    private String shippingCity;

    @TableField("shipping_state")
    private String shippingState;

    @TableField("shipping_postal_code")
    private String shippingPostalCode;

    @TableField("shipping_country")
    private String shippingCountry;

    @TableField("shipping_phone")
    private String shippingPhone;

    // Order items will be handled separately via OrderItemMapper
    @TableField(exist = false)
    private List<OrderItem> orderItems = new ArrayList<>();

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableField("confirmed_at")
    private LocalDateTime confirmedAt;

    @TableField("shipped_at")
    private LocalDateTime shippedAt;

    @TableField("delivered_at")
    private LocalDateTime deliveredAt;

    @TableField("cancelled_at")
    private LocalDateTime cancelledAt;

    @Version
    @TableField("version")
    private Long version;

    // Constructors
    public Order() {}

    public Order(Long userId, String orderNumber) {
        this.userId = userId;
        this.orderNumber = orderNumber;
        this.status = OrderStatus.PENDING;
    }

    // Domain methods
    public void addOrderItem(OrderItem orderItem) {
        orderItems.add(orderItem);
        orderItem.setOrder(this);
        recalculateTotal();
    }

    public void removeOrderItem(OrderItem orderItem) {
        orderItems.remove(orderItem);
        orderItem.setOrder(null);
        recalculateTotal();
    }

    public void confirm() {
        if (this.status == OrderStatus.PENDING) {
            this.status = OrderStatus.CONFIRMED;
            this.confirmedAt = LocalDateTime.now();
        } else {
            throw new IllegalStateException("Order can only be confirmed from PENDING status");
        }
    }

    public void ship() {
        if (this.status == OrderStatus.CONFIRMED) {
            this.status = OrderStatus.SHIPPED;
            this.shippedAt = LocalDateTime.now();
        } else {
            throw new IllegalStateException("Order can only be shipped from CONFIRMED status");
        }
    }

    public void deliver() {
        if (this.status == OrderStatus.SHIPPED) {
            this.status = OrderStatus.DELIVERED;
            this.deliveredAt = LocalDateTime.now();
        } else {
            throw new IllegalStateException("Order can only be delivered from SHIPPED status");
        }
    }

    public void cancel() {
        if (this.status == OrderStatus.PENDING || this.status == OrderStatus.CONFIRMED) {
            this.status = OrderStatus.CANCELLED;
            this.cancelledAt = LocalDateTime.now();
        } else {
            throw new IllegalStateException("Order can only be cancelled from PENDING or CONFIRMED status");
        }
    }

    public boolean canBeModified() {
        return this.status == OrderStatus.PENDING;
    }

    public boolean isActive() {
        return this.status != OrderStatus.CANCELLED && this.status != OrderStatus.DELIVERED;
    }

    private void recalculateTotal() {
        BigDecimal itemsTotal = orderItems.stream()
            .map(OrderItem::getTotalPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        this.totalAmount = itemsTotal
            .add(taxAmount)
            .add(shippingAmount)
            .subtract(discountAmount);
    }

    public BigDecimal getSubtotal() {
        return orderItems.stream()
            .map(OrderItem::getTotalPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public String getOrderNumber() { return orderNumber; }
    public void setOrderNumber(String orderNumber) { this.orderNumber = orderNumber; }

    public OrderStatus getStatus() { return status; }
    public void setStatus(OrderStatus status) { this.status = status; }

    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }

    public BigDecimal getTaxAmount() { return taxAmount; }
    public void setTaxAmount(BigDecimal taxAmount) { this.taxAmount = taxAmount; }

    public BigDecimal getShippingAmount() { return shippingAmount; }
    public void setShippingAmount(BigDecimal shippingAmount) { this.shippingAmount = shippingAmount; }

    public BigDecimal getDiscountAmount() { return discountAmount; }
    public void setDiscountAmount(BigDecimal discountAmount) { this.discountAmount = discountAmount; }

    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }

    public ShippingAddress getShippingAddress() { return shippingAddress; }
    public void setShippingAddress(ShippingAddress shippingAddress) { this.shippingAddress = shippingAddress; }

    public List<OrderItem> getOrderItems() { return orderItems; }
    public void setOrderItems(List<OrderItem> orderItems) { this.orderItems = orderItems; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public LocalDateTime getConfirmedAt() { return confirmedAt; }
    public void setConfirmedAt(LocalDateTime confirmedAt) { this.confirmedAt = confirmedAt; }

    public LocalDateTime getShippedAt() { return shippedAt; }
    public void setShippedAt(LocalDateTime shippedAt) { this.shippedAt = shippedAt; }

    public LocalDateTime getDeliveredAt() { return deliveredAt; }
    public void setDeliveredAt(LocalDateTime deliveredAt) { this.deliveredAt = deliveredAt; }

    public LocalDateTime getCancelledAt() { return cancelledAt; }
    public void setCancelledAt(LocalDateTime cancelledAt) { this.cancelledAt = cancelledAt; }

    public Long getVersion() { return version; }
    public void setVersion(Long version) { this.version = version; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Order order = (Order) o;
        return Objects.equals(id, order.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Order{" +
                "id=" + id +
                ", orderNumber='" + orderNumber + '\'' +
                ", status=" + status +
                ", totalAmount=" + totalAmount +
                '}';
    }
}
