package com.enterprise.microservices.order.domain;

/**
 * Order Status Enumeration
 * Represents the possible states of an order in the system
 */
public enum OrderStatus {
    PENDING("Pending"),
    CONFIRMED("Confirmed"),
    PROCESSING("Processing"),
    SHIPPED("Shipped"),
    DELIVERED("Delivered"),
    CANCELLED("Cancelled"),
    REFUNDED("Refunded");

    private final String displayName;

    OrderStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public boolean isActive() {
        return this != CANCELLED && this != DELIVERED && this != REFUNDED;
    }

    public boolean canBeCancelled() {
        return this == PENDING || this == CONFIRMED;
    }

    public boolean canBeShipped() {
        return this == CONFIRMED || this == PROCESSING;
    }

    public boolean canBeDelivered() {
        return this == SHIPPED;
    }
}
