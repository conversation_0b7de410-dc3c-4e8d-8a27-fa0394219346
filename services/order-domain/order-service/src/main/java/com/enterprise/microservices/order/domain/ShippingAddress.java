package com.enterprise.microservices.order.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * Shipping Address Value Object
 * Represents shipping address information for orders
 */
@Embeddable
public class ShippingAddress {

    @Column(name = "shipping_first_name", length = 100)
    @NotBlank(message = "First name is required")
    private String firstName;

    @Column(name = "shipping_last_name", length = 100)
    @NotBlank(message = "Last name is required")
    private String lastName;

    @Column(name = "shipping_address_line1", length = 200)
    @NotBlank(message = "Address line 1 is required")
    private String addressLine1;

    @Column(name = "shipping_address_line2", length = 200)
    private String addressLine2;

    @Column(name = "shipping_city", length = 100)
    @NotBlank(message = "City is required")
    private String city;

    @Column(name = "shipping_state", length = 100)
    @NotBlank(message = "State is required")
    private String state;

    @Column(name = "shipping_postal_code", length = 20)
    @NotBlank(message = "Postal code is required")
    private String postalCode;

    @Column(name = "shipping_country", length = 100)
    @NotBlank(message = "Country is required")
    private String country;

    @Column(name = "shipping_phone", length = 20)
    private String phone;

    // Constructors
    public ShippingAddress() {}

    public ShippingAddress(String firstName, String lastName, String addressLine1, 
                          String city, String state, String postalCode, String country) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.addressLine1 = addressLine1;
        this.city = city;
        this.state = state;
        this.postalCode = postalCode;
        this.country = country;
    }

    // Domain methods
    public String getFullName() {
        return firstName + " " + lastName;
    }

    public String getFullAddress() {
        StringBuilder address = new StringBuilder();
        address.append(addressLine1);
        if (addressLine2 != null && !addressLine2.trim().isEmpty()) {
            address.append(", ").append(addressLine2);
        }
        address.append(", ").append(city);
        address.append(", ").append(state);
        address.append(" ").append(postalCode);
        address.append(", ").append(country);
        return address.toString();
    }

    // Getters and Setters
    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getAddressLine1() { return addressLine1; }
    public void setAddressLine1(String addressLine1) { this.addressLine1 = addressLine1; }

    public String getAddressLine2() { return addressLine2; }
    public void setAddressLine2(String addressLine2) { this.addressLine2 = addressLine2; }

    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }

    public String getState() { return state; }
    public void setState(String state) { this.state = state; }

    public String getPostalCode() { return postalCode; }
    public void setPostalCode(String postalCode) { this.postalCode = postalCode; }

    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
}
