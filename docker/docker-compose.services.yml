version: '3.8'

networks:
  microservices-network:
    external: true

services:
  # Nacos Cluster
  nacos-1:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-1
    environment:
      MODE: cluster
      NACOS_SERVERS: nacos-1:8848 nacos-2:8848 nacos-3:8848
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql-master
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos-1-logs:/home/<USER>/logs
      - ./infrastructure/nacos/nacos-cluster.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - mysql-master
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nacos-2:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-2
    environment:
      MODE: cluster
      NACOS_SERVERS: nacos-1:8848 nacos-2:8848 nacos-3:8848
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql-master
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos-2-logs:/home/<USER>/logs
      - ./infrastructure/nacos/nacos-cluster.properties:/home/<USER>/conf/application.properties
    ports:
      - "8849:8848"
      - "9849:9848"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - mysql-master
    restart: unless-stopped

  nacos-3:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-3
    environment:
      MODE: cluster
      NACOS_SERVERS: nacos-1:8848 nacos-2:8848 nacos-3:8848
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql-master
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos-3-logs:/home/<USER>/logs
      - ./infrastructure/nacos/nacos-cluster.properties:/home/<USER>/conf/application.properties
    ports:
      - "8850:8848"
      - "9850:9848"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - mysql-master
    restart: unless-stopped

  # Apache Pulsar Cluster
  pulsar-broker-1:
    image: apachepulsar/pulsar:3.1.0
    container_name: pulsar-broker-1
    command: bin/pulsar broker
    environment:
      PULSAR_MEM: "-Xms1g -Xmx1g -XX:MaxDirectMemorySize=1g"
    volumes:
      - pulsar-broker-1-data:/pulsar/data
      - ./infrastructure/pulsar/pulsar-cluster.conf:/pulsar/conf/broker.conf
    ports:
      - "6650:6650"
      - "8080:8080"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - zookeeper-1
      - zookeeper-2
      - zookeeper-3
    restart: unless-stopped

  # BookKeeper for Pulsar
  bookkeeper-1:
    image: apachepulsar/pulsar:3.1.0
    container_name: bookkeeper-1
    command: bin/pulsar bookie
    environment:
      PULSAR_MEM: "-Xms1g -Xmx1g"
    volumes:
      - bookkeeper-1-data:/pulsar/data
    ports:
      - "3181:3181"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - zookeeper-1
      - zookeeper-2
      - zookeeper-3
    restart: unless-stopped

  # APISIX Gateway
  etcd:
    image: bitnami/etcd:3.5.9
    container_name: etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://etcd:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2379"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  apisix:
    image: apache/apisix:3.6.0-debian
    container_name: apisix
    volumes:
      - ./infrastructure/apisix/apisix.yaml:/usr/local/apisix/conf/apisix.yaml:ro
    ports:
      - "9080:9080"
      - "9180:9180"
      - "9091:9091"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - etcd
    restart: unless-stopped

  # Sentinel Dashboard
  sentinel-dashboard:
    image: bladex/sentinel-dashboard:1.8.6
    container_name: sentinel-dashboard
    ports:
      - "8858:8858"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.1.0
    container_name: grafana
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  nacos-1-logs:
  nacos-2-logs:
  nacos-3-logs:
  pulsar-broker-1-data:
  bookkeeper-1-data:
  prometheus-data:
  grafana-data:
