version: '3.8'

networks:
  microservices-network:
    external: true

services:
  # Nacos Cluster
  nacos-1:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-1
    environment:
      MODE: cluster
      NACOS_SERVERS: nacos-1:8848 nacos-2:8848 nacos-3:8848
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql-master
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos-1-logs:/home/<USER>/logs
      - ./infrastructure/nacos/nacos-cluster.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - mysql-master
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8848/nacos/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nacos-2:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-2
    environment:
      MODE: cluster
      NACOS_SERVERS: nacos-1:8848 nacos-2:8848 nacos-3:8848
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql-master
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos-2-logs:/home/<USER>/logs
      - ./infrastructure/nacos/nacos-cluster.properties:/home/<USER>/conf/application.properties
    ports:
      - "8849:8848"
      - "9849:9848"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - mysql-master
    restart: unless-stopped

  nacos-3:
    image: nacos/nacos-server:v2.2.3
    container_name: nacos-3
    environment:
      MODE: cluster
      NACOS_SERVERS: nacos-1:8848 nacos-2:8848 nacos-3:8848
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql-master
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: nacos
      MYSQL_SERVICE_PASSWORD: nacos
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos-3-logs:/home/<USER>/logs
      - ./infrastructure/nacos/nacos-cluster.properties:/home/<USER>/conf/application.properties
    ports:
      - "8850:8848"
      - "9850:9848"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - mysql-master
    restart: unless-stopped

  # Apache RocketMQ Cluster
  rocketmq-nameserver-1:
    image: apache/rocketmq:5.1.4
    container_name: rocketmq-nameserver-1
    command: sh mqnamesrv
    environment:
      JAVA_OPT_EXT: "-Xms512m -Xmx512m"
    volumes:
      - rocketmq-nameserver-1-logs:/home/<USER>/logs
    ports:
      - "9876:9876"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  rocketmq-nameserver-2:
    image: apache/rocketmq:5.1.4
    container_name: rocketmq-nameserver-2
    command: sh mqnamesrv
    environment:
      JAVA_OPT_EXT: "-Xms512m -Xmx512m"
    volumes:
      - rocketmq-nameserver-2-logs:/home/<USER>/logs
    ports:
      - "9877:9876"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  rocketmq-broker-1:
    image: apache/rocketmq:5.1.4
    container_name: rocketmq-broker-1
    command: sh mqbroker -n rocketmq-nameserver-1:9876;rocketmq-nameserver-2:9876 -c /home/<USER>/conf/broker.conf
    environment:
      JAVA_OPT_EXT: "-Xms1g -Xmx1g"
    volumes:
      - rocketmq-broker-1-data:/home/<USER>/store
      - rocketmq-broker-1-logs:/home/<USER>/logs
      - ./infrastructure/rocketmq/broker.conf:/home/<USER>/conf/broker.conf
    ports:
      - "10909:10909"
      - "10911:10911"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - rocketmq-nameserver-1
      - rocketmq-nameserver-2
    restart: unless-stopped

  rocketmq-console:
    image: styletang/rocketmq-console-ng:latest
    container_name: rocketmq-console
    environment:
      JAVA_OPTS: "-Xms256m -Xmx256m -Drocketmq.namesrv.addr=rocketmq-nameserver-1:9876;rocketmq-nameserver-2:9876"
    ports:
      - "8082:8080"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - rocketmq-nameserver-1
      - rocketmq-nameserver-2
    restart: unless-stopped

  # APISIX Gateway
  etcd:
    image: bitnami/etcd:3.5.9
    container_name: etcd
    environment:
      ETCD_ENABLE_V2: "true"
      ALLOW_NONE_AUTHENTICATION: "yes"
      ETCD_ADVERTISE_CLIENT_URLS: "http://etcd:2379"
      ETCD_LISTEN_CLIENT_URLS: "http://0.0.0.0:2379"
    ports:
      - "2379:2379"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  apisix:
    image: apache/apisix:3.6.0-debian
    container_name: apisix
    volumes:
      - ./infrastructure/apisix/apisix.yaml:/usr/local/apisix/conf/apisix.yaml:ro
    ports:
      - "9080:9080"
      - "9180:9180"
      - "9091:9091"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - etcd
    restart: unless-stopped

  # Sentinel Dashboard
  sentinel-dashboard:
    image: bladex/sentinel-dashboard:1.8.6
    container_name: sentinel-dashboard
    ports:
      - "8858:8858"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  # Monitoring Stack
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./infrastructure/monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    ports:
      - "9090:9090"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  grafana:
    image: grafana/grafana:10.1.0
    container_name: grafana
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./infrastructure/monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./infrastructure/monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3000:3000"
    networks:
      microservices-network:
        ipv4_address: ***********
    depends_on:
      - prometheus
    restart: unless-stopped

volumes:
  nacos-1-logs:
  nacos-2-logs:
  nacos-3-logs:
  rocketmq-nameserver-1-logs:
  rocketmq-nameserver-2-logs:
  rocketmq-broker-1-data:
  rocketmq-broker-1-logs:
  prometheus-data:
  grafana-data:
