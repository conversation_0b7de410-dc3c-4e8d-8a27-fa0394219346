version: '3.8'

networks:
  microservices-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

services:
  # MySQL Master
  mysql-master:
    image: mysql:8.0
    container_name: mysql-master
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: microservices
      MYSQL_USER: microservices
      MYSQL_PASSWORD: password
    volumes:
      - mysql-master-data:/var/lib/mysql
      - ./infrastructure/mysql/mysql-master.cnf:/etc/mysql/conf.d/mysql.cnf
      - ./infrastructure/mysql/init-master.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      microservices-network:
        ipv4_address: **********
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # MySQL Slave 1
  mysql-slave-1:
    image: mysql:8.0
    container_name: mysql-slave-1
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: microservices
      MYSQL_USER: microservices
      MYSQL_PASSWORD: password
    volumes:
      - mysql-slave-1-data:/var/lib/mysql
      - ./infrastructure/mysql/mysql-slave.cnf:/etc/mysql/conf.d/mysql.cnf
    ports:
      - "3307:3306"
    networks:
      microservices-network:
        ipv4_address: **********
    depends_on:
      mysql-master:
        condition: service_healthy
    restart: unless-stopped

  # Redis Cluster Nodes
  redis-1:
    image: redis:7-alpine
    container_name: redis-1
    command: redis-server /etc/redis/redis.conf --port 7001
    environment:
      REDIS_ANNOUNCE_IP: ***********
      REDIS_ANNOUNCE_PORT: 7001
      REDIS_ANNOUNCE_BUS_PORT: 17001
      REDIS_PASSWORD: redispassword
    volumes:
      - redis-1-data:/data
      - ./infrastructure/redis/redis-cluster.conf:/etc/redis/redis.conf
    ports:
      - "7001:7001"
      - "17001:17001"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  redis-2:
    image: redis:7-alpine
    container_name: redis-2
    command: redis-server /etc/redis/redis.conf --port 7002
    environment:
      REDIS_ANNOUNCE_IP: ***********
      REDIS_ANNOUNCE_PORT: 7002
      REDIS_ANNOUNCE_BUS_PORT: 17002
      REDIS_PASSWORD: redispassword
    volumes:
      - redis-2-data:/data
      - ./infrastructure/redis/redis-cluster.conf:/etc/redis/redis.conf
    ports:
      - "7002:7002"
      - "17002:17002"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  redis-3:
    image: redis:7-alpine
    container_name: redis-3
    command: redis-server /etc/redis/redis.conf --port 7003
    environment:
      REDIS_ANNOUNCE_IP: ***********
      REDIS_ANNOUNCE_PORT: 7003
      REDIS_ANNOUNCE_BUS_PORT: 17003
      REDIS_PASSWORD: redispassword
    volumes:
      - redis-3-data:/data
      - ./infrastructure/redis/redis-cluster.conf:/etc/redis/redis.conf
    ports:
      - "7003:7003"
      - "17003:17003"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  redis-4:
    image: redis:7-alpine
    container_name: redis-4
    command: redis-server /etc/redis/redis.conf --port 7004
    environment:
      REDIS_ANNOUNCE_IP: ***********
      REDIS_ANNOUNCE_PORT: 7004
      REDIS_ANNOUNCE_BUS_PORT: 17004
      REDIS_PASSWORD: redispassword
    volumes:
      - redis-4-data:/data
      - ./infrastructure/redis/redis-cluster.conf:/etc/redis/redis.conf
    ports:
      - "7004:7004"
      - "17004:17004"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  redis-5:
    image: redis:7-alpine
    container_name: redis-5
    command: redis-server /etc/redis/redis.conf --port 7005
    environment:
      REDIS_ANNOUNCE_IP: ***********
      REDIS_ANNOUNCE_PORT: 7005
      REDIS_ANNOUNCE_BUS_PORT: 17005
      REDIS_PASSWORD: redispassword
    volumes:
      - redis-5-data:/data
      - ./infrastructure/redis/redis-cluster.conf:/etc/redis/redis.conf
    ports:
      - "7005:7005"
      - "17005:17005"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  redis-6:
    image: redis:7-alpine
    container_name: redis-6
    command: redis-server /etc/redis/redis.conf --port 7006
    environment:
      REDIS_ANNOUNCE_IP: ***********
      REDIS_ANNOUNCE_PORT: 7006
      REDIS_ANNOUNCE_BUS_PORT: 17006
      REDIS_PASSWORD: redispassword
    volumes:
      - redis-6-data:/data
      - ./infrastructure/redis/redis-cluster.conf:/etc/redis/redis.conf
    ports:
      - "7006:7006"
      - "17006:17006"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

  # Redis Cluster Setup
  redis-cluster-setup:
    image: redis:7-alpine
    container_name: redis-cluster-setup
    command: >
      sh -c "
        sleep 10 &&
        redis-cli --cluster create 
        ***********:7001 ***********:7002 ***********:7003 
        ***********:7004 ***********:7005 ***********:7006 
        --cluster-replicas 1 --cluster-yes
      "
    networks:
      - microservices-network
    depends_on:
      - redis-1
      - redis-2
      - redis-3
      - redis-4
      - redis-5
      - redis-6

  # ZooKeeper Cluster for Pulsar
  zookeeper-1:
    image: zookeeper:3.8
    container_name: zookeeper-1
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zookeeper-1:2888:3888;2181 server.2=zookeeper-2:2888:3888;2181 server.3=zookeeper-3:2888:3888;2181
    volumes:
      - zookeeper-1-data:/data
      - zookeeper-1-logs:/datalog
    ports:
      - "2181:2181"
    networks:
      microservices-network:
        ipv4_address: **********0
    restart: unless-stopped

  zookeeper-2:
    image: zookeeper:3.8
    container_name: zookeeper-2
    environment:
      ZOO_MY_ID: 2
      ZOO_SERVERS: server.1=zookeeper-1:2888:3888;2181 server.2=zookeeper-2:2888:3888;2181 server.3=zookeeper-3:2888:3888;2181
    volumes:
      - zookeeper-2-data:/data
      - zookeeper-2-logs:/datalog
    ports:
      - "2182:2181"
    networks:
      microservices-network:
        ipv4_address: **********1
    restart: unless-stopped

  zookeeper-3:
    image: zookeeper:3.8
    container_name: zookeeper-3
    environment:
      ZOO_MY_ID: 3
      ZOO_SERVERS: server.1=zookeeper-1:2888:3888;2181 server.2=zookeeper-2:2888:3888;2181 server.3=zookeeper-3:2888:3888;2181
    volumes:
      - zookeeper-3-data:/data
      - zookeeper-3-logs:/datalog
    ports:
      - "2183:2181"
    networks:
      microservices-network:
        ipv4_address: ***********
    restart: unless-stopped

volumes:
  mysql-master-data:
  mysql-slave-1-data:
  redis-1-data:
  redis-2-data:
  redis-3-data:
  redis-4-data:
  redis-5-data:
  redis-6-data:
  zookeeper-1-data:
  zookeeper-1-logs:
  zookeeper-2-data:
  zookeeper-2-logs:
  zookeeper-3-data:
  zookeeper-3-logs:
