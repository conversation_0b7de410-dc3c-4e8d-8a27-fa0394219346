apiVersion: v1
kind: ConfigMap
metadata:
  name: rocketmq-broker-config
  namespace: microservices-infrastructure
data:
  broker.conf: |
    clusterName=DefaultCluster
    brokerName=broker-a
    brokerId=0
    deleteWhen=04
    fileReservedTime=48
    brokerRole=ASYNC_MASTER
    flushDiskType=ASYNC_FLUSH
    listenPort=10911
    namesrvAddr=rocketmq-nameserver:9876
    storePathRootDir=/home/<USER>/store
    storePathCommitLog=/home/<USER>/store/commitlog
    storePathConsumeQueue=/home/<USER>/store/consumequeue
    storePathIndex=/home/<USER>/store/index
    mapedFileSizeCommitLog=1073741824
    mapedFileSizeConsumeQueue=300000
    flushCommitLogLeastPages=4
    flushConsumeQueueLeastPages=2
    flushCommitLogThoroughInterval=10000
    flushConsumeQueueThoroughInterval=60000
    maxMessageSize=65536
    autoCreateTopicEnable=true
    autoCreateSubscriptionGroup=true
    defaultTopicQueueNums=8

---
apiVersion: v1
kind: Service
metadata:
  name: rocketmq-nameserver
  namespace: microservices-infrastructure
  labels:
    app: rocketmq-nameserver
spec:
  ports:
  - port: 9876
    targetPort: 9876
    name: nameserver
  clusterIP: None
  selector:
    app: rocketmq-nameserver

---
apiVersion: v1
kind: Service
metadata:
  name: rocketmq-broker
  namespace: microservices-infrastructure
  labels:
    app: rocketmq-broker
spec:
  ports:
  - port: 10909
    targetPort: 10909
    name: vip
  - port: 10911
    targetPort: 10911
    name: broker
  - port: 10912
    targetPort: 10912
    name: ha
  clusterIP: None
  selector:
    app: rocketmq-broker

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rocketmq-nameserver
  namespace: microservices-infrastructure
spec:
  serviceName: rocketmq-nameserver
  replicas: 2
  selector:
    matchLabels:
      app: rocketmq-nameserver
  template:
    metadata:
      labels:
        app: rocketmq-nameserver
    spec:
      containers:
      - name: nameserver
        image: apache/rocketmq:5.1.4
        command: ["sh", "mqnamesrv"]
        env:
        - name: JAVA_OPT_EXT
          value: "-Xms512m -Xmx512m"
        ports:
        - containerPort: 9876
          name: nameserver
        volumeMounts:
        - name: nameserver-storage
          mountPath: /home/<USER>/logs
        livenessProbe:
          tcpSocket:
            port: 9876
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          tcpSocket:
            port: 9876
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
  volumeClaimTemplates:
  - metadata:
      name: nameserver-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 10Gi

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: rocketmq-broker
  namespace: microservices-infrastructure
spec:
  serviceName: rocketmq-broker
  replicas: 2
  selector:
    matchLabels:
      app: rocketmq-broker
  template:
    metadata:
      labels:
        app: rocketmq-broker
    spec:
      containers:
      - name: broker
        image: apache/rocketmq:5.1.4
        command: 
        - sh
        - mqbroker
        - -n
        - rocketmq-nameserver-0.rocketmq-nameserver:9876;rocketmq-nameserver-1.rocketmq-nameserver:9876
        - -c
        - /home/<USER>/conf/broker.conf
        env:
        - name: JAVA_OPT_EXT
          value: "-Xms1g -Xmx1g"
        ports:
        - containerPort: 10909
          name: vip
        - containerPort: 10911
          name: broker
        - containerPort: 10912
          name: ha
        volumeMounts:
        - name: broker-storage
          mountPath: /home/<USER>/store
        - name: broker-logs
          mountPath: /home/<USER>/logs
        - name: broker-config
          mountPath: /home/<USER>/conf
        livenessProbe:
          tcpSocket:
            port: 10911
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          tcpSocket:
            port: 10911
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
      volumes:
      - name: broker-config
        configMap:
          name: rocketmq-broker-config
  volumeClaimTemplates:
  - metadata:
      name: broker-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 50Gi
  - metadata:
      name: broker-logs
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: rocketmq-console
  namespace: microservices-infrastructure
  labels:
    app: rocketmq-console
spec:
  type: ClusterIP
  ports:
  - port: 8080
    targetPort: 8080
    name: console
  selector:
    app: rocketmq-console

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rocketmq-console
  namespace: microservices-infrastructure
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rocketmq-console
  template:
    metadata:
      labels:
        app: rocketmq-console
    spec:
      containers:
      - name: console
        image: styletang/rocketmq-console-ng:latest
        env:
        - name: JAVA_OPTS
          value: "-Xms256m -Xmx256m -Drocketmq.namesrv.addr=rocketmq-nameserver-0.rocketmq-nameserver:9876;rocketmq-nameserver-1.rocketmq-nameserver:9876"
        ports:
        - containerPort: 8080
          name: console
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
