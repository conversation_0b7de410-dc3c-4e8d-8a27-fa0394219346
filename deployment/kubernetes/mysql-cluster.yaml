apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-master-config
  namespace: microservices-infrastructure
data:
  my.cnf: |
    [mysqld]
    server-id = 1
    bind-address = 0.0.0.0
    port = 3306
    log-bin = mysql-bin
    binlog-format = ROW
    gtid-mode = ON
    enforce-gtid-consistency = ON
    log-slave-updates = ON
    sync_binlog = 1
    innodb_flush_log_at_trx_commit = 1
    innodb_buffer_pool_size = 2G
    innodb_buffer_pool_instances = 8
    innodb_log_file_size = 256M
    innodb_log_buffer_size = 64M
    innodb_flush_method = O_DIRECT
    innodb_file_per_table = 1
    max_connections = 1000
    character-set-server = utf8mb4
    collation-server = utf8mb4_unicode_ci
    default-time-zone = '+00:00'
    sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

---
apiVersion: v1
kind: Secret
metadata:
  name: mysql-secret
  namespace: microservices-infrastructure
type: Opaque
data:
  mysql-root-password: cm9vdHBhc3N3b3Jk  # rootpassword
  mysql-password: cGFzc3dvcmQ=  # password
  replication-password: cmVwbGljYXRpb25fcGFzc3dvcmQ=  # replication_password

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-master
  namespace: microservices-infrastructure
  labels:
    app: mysql
    role: master
spec:
  ports:
  - port: 3306
    targetPort: 3306
  selector:
    app: mysql
    role: master
  type: ClusterIP

---
apiVersion: v1
kind: Service
metadata:
  name: mysql-slave
  namespace: microservices-infrastructure
  labels:
    app: mysql
    role: slave
spec:
  ports:
  - port: 3306
    targetPort: 3306
  selector:
    app: mysql
    role: slave
  type: ClusterIP

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql-master
  namespace: microservices-infrastructure
spec:
  serviceName: mysql-master
  replicas: 1
  selector:
    matchLabels:
      app: mysql
      role: master
  template:
    metadata:
      labels:
        app: mysql
        role: master
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: mysql-root-password
        - name: MYSQL_DATABASE
          value: microservices
        - name: MYSQL_USER
          value: microservices
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: mysql-password
        ports:
        - containerPort: 3306
          name: mysql
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
        - name: mysql-config
          mountPath: /etc/mysql/conf.d
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - mysql
            - -h
            - localhost
            - -e
            - SELECT 1
          initialDelaySeconds: 5
          periodSeconds: 2
          timeoutSeconds: 1
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: mysql-config
        configMap:
          name: mysql-master-config
  volumeClaimTemplates:
  - metadata:
      name: mysql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mysql-slave
  namespace: microservices-infrastructure
spec:
  serviceName: mysql-slave
  replicas: 2
  selector:
    matchLabels:
      app: mysql
      role: slave
  template:
    metadata:
      labels:
        app: mysql
        role: slave
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        env:
        - name: MYSQL_ROOT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: mysql-root-password
        - name: MYSQL_DATABASE
          value: microservices
        - name: MYSQL_USER
          value: microservices
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: mysql-password
        ports:
        - containerPort: 3306
          name: mysql
        volumeMounts:
        - name: mysql-data
          mountPath: /var/lib/mysql
        - name: mysql-slave-config
          mountPath: /etc/mysql/conf.d
        livenessProbe:
          exec:
            command:
            - mysqladmin
            - ping
            - -h
            - localhost
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - mysql
            - -h
            - localhost
            - -e
            - SELECT 1
          initialDelaySeconds: 5
          periodSeconds: 2
          timeoutSeconds: 1
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
      volumes:
      - name: mysql-slave-config
        configMap:
          name: mysql-slave-config
  volumeClaimTemplates:
  - metadata:
      name: mysql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 100Gi

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: mysql-slave-config
  namespace: microservices-infrastructure
data:
  my.cnf: |
    [mysqld]
    server-id = 2
    bind-address = 0.0.0.0
    port = 3306
    read-only = 1
    super-read-only = 1
    relay-log = relay-bin
    relay-log-index = relay-bin.index
    relay-log-info-file = relay-log.info
    relay-log-recovery = ON
    relay-log-purge = ON
    gtid-mode = ON
    enforce-gtid-consistency = ON
    log-slave-updates = ON
    slave-parallel-type = LOGICAL_CLOCK
    slave-parallel-workers = 8
    slave-preserve-commit-order = ON
    innodb_flush_log_at_trx_commit = 2
    innodb_buffer_pool_size = 2G
    innodb_buffer_pool_instances = 8
    innodb_log_file_size = 256M
    innodb_log_buffer_size = 64M
    innodb_flush_method = O_DIRECT
    innodb_file_per_table = 1
    max_connections = 1000
    character-set-server = utf8mb4
    collation-server = utf8mb4_unicode_ci
    default-time-zone = '+00:00'
    sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO
