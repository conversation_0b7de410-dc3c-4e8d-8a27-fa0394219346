apiVersion: v1
kind: ConfigMap
metadata:
  name: user-service-config
  namespace: microservices
data:
  application.yml: |
    server:
      port: 8081
      servlet:
        context-path: /user-service
    
    spring:
      application:
        name: user-service
      profiles:
        active: production
      
      datasource:
        url: *******************************************************************************************************************************************************
        username: microservices
        password: ${DB_PASSWORD}
        driver-class-name: com.mysql.cj.jdbc.Driver
        hikari:
          maximum-pool-size: 20
          minimum-idle: 5
          idle-timeout: 300000
          connection-timeout: 20000
          leak-detection-threshold: 60000
      
      jpa:
        hibernate:
          ddl-auto: validate
        show-sql: false
        properties:
          hibernate:
            dialect: org.hibernate.dialect.MySQL8Dialect
            format_sql: true
            use_sql_comments: true
            jdbc:
              batch_size: 20
            order_inserts: true
            order_updates: true
      
      data:
        redis:
          cluster:
            nodes:
              - redis-cluster-0.redis-cluster.microservices-infrastructure.svc.cluster.local:6379
              - redis-cluster-1.redis-cluster.microservices-infrastructure.svc.cluster.local:6379
              - redis-cluster-2.redis-cluster.microservices-infrastructure.svc.cluster.local:6379
              - redis-cluster-3.redis-cluster.microservices-infrastructure.svc.cluster.local:6379
              - redis-cluster-4.redis-cluster.microservices-infrastructure.svc.cluster.local:6379
              - redis-cluster-5.redis-cluster.microservices-infrastructure.svc.cluster.local:6379
          password: ${REDIS_PASSWORD}
          timeout: 2000ms
          lettuce:
            pool:
              max-active: 8
              max-idle: 8
              min-idle: 0
              max-wait: -1ms
      
      cache:
        type: redis
        redis:
          time-to-live: 600000
          cache-null-values: false
      
      cloud:
        nacos:
          discovery:
            server-addr: nacos-headless.microservices-infrastructure.svc.cluster.local:8848
            namespace: production
            group: DEFAULT_GROUP
            cluster-name: default
            metadata:
              version: 1.0.0
              region: ${REGION:us-east-1}
              zone: ${ZONE:us-east-1a}
          config:
            server-addr: nacos-headless.microservices-infrastructure.svc.cluster.local:8848
            file-extension: yml
            namespace: production
            group: DEFAULT_GROUP
        
        sentinel:
          transport:
            dashboard: sentinel-dashboard.microservices-infrastructure.svc.cluster.local:8080
            port: 8719
          datasource:
            flow:
              nacos:
                server-addr: nacos-headless.microservices-infrastructure.svc.cluster.local:8848
                dataId: user-service-flow-rules
                groupId: SENTINEL_GROUP
                rule-type: flow
            degrade:
              nacos:
                server-addr: nacos-headless.microservices-infrastructure.svc.cluster.local:8848
                dataId: user-service-degrade-rules
                groupId: SENTINEL_GROUP
                rule-type: degrade
    
    pulsar:
      service-url: pulsar://pulsar-broker.microservices-infrastructure.svc.cluster.local:6650
      topics:
        user-events: user-events
    
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
      endpoint:
        health:
          show-details: always
      metrics:
        export:
          prometheus:
            enabled: true
    
    logging:
      level:
        com.enterprise.microservices.user: INFO
        org.springframework.security: WARN
        org.hibernate.SQL: WARN
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
      file:
        name: /var/log/user-service.log

---
apiVersion: v1
kind: Secret
metadata:
  name: user-service-secret
  namespace: microservices
type: Opaque
data:
  db-password: cGFzc3dvcmQ=  # password
  redis-password: cmVkaXNwYXNzd29yZA==  # redispassword
  jwt-secret: bXlTZWNyZXRLZXk=  # mySecretKey

---
apiVersion: v1
kind: Service
metadata:
  name: user-service
  namespace: microservices
  labels:
    app: user-service
spec:
  type: ClusterIP
  ports:
  - port: 8081
    targetPort: 8081
    protocol: TCP
    name: http
  selector:
    app: user-service

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service
  namespace: microservices
  labels:
    app: user-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: user-service
  template:
    metadata:
      labels:
        app: user-service
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8081"
        prometheus.io/path: "/user-service/actuator/prometheus"
    spec:
      containers:
      - name: user-service
        image: enterprise/user-service:1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8081
          name: http
        env:
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: user-service-secret
              key: db-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: user-service-secret
              key: redis-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: user-service-secret
              key: jwt-secret
        - name: REGION
          value: "us-east-1"
        - name: ZONE
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['topology.kubernetes.io/zone']
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /var/log
        livenessProbe:
          httpGet:
            path: /user-service/actuator/health/liveness
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /user-service/actuator/health/readiness
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: config
        configMap:
          name: user-service-config
      - name: logs
        emptyDir: {}

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: user-service-hpa
  namespace: microservices
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: user-service
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
