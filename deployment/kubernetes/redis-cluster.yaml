apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-cluster-config
  namespace: microservices-infrastructure
data:
  redis.conf: |
    bind 0.0.0.0
    port 6379
    protected-mode no
    cluster-enabled yes
    cluster-config-file nodes.conf
    cluster-node-timeout 15000
    appendonly yes
    appendfilename "appendonly.aof"
    appendfsync everysec
    save 900 1
    save 300 10
    save 60 10000
    maxmemory 2gb
    maxmemory-policy allkeys-lru
    tcp-keepalive 300
    timeout 0
    databases 16
    slowlog-log-slower-than 10000
    slowlog-max-len 128
    hash-max-ziplist-entries 512
    hash-max-ziplist-value 64
    list-max-ziplist-size -2
    set-max-intset-entries 512
    zset-max-ziplist-entries 128
    zset-max-ziplist-value 64
    activerehashing yes
    client-query-buffer-limit 1gb
    proto-max-bulk-len 512mb
    io-threads 4
    io-threads-do-reads yes

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: microservices-infrastructure
type: Opaque
data:
  redis-password: cmVkaXNwYXNzd29yZA==  # redispassword

---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster
  namespace: microservices-infrastructure
  labels:
    app: redis-cluster
spec:
  ports:
  - port: 6379
    targetPort: 6379
    name: client
  - port: 16379
    targetPort: 16379
    name: gossip
  clusterIP: None
  selector:
    app: redis-cluster

---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: microservices-infrastructure
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --requirepass
        - $(REDIS_PASSWORD)
        - --masterauth
        - $(REDIS_PASSWORD)
        - --cluster-announce-ip
        - $(POD_IP)
        env:
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: redis-password
        ports:
        - containerPort: 6379
          name: client
        - containerPort: 16379
          name: gossip
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 2
          timeoutSeconds: 1
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: redis-config
        configMap:
          name: redis-cluster-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: fast-ssd
      resources:
        requests:
          storage: 20Gi

---
apiVersion: batch/v1
kind: Job
metadata:
  name: redis-cluster-init
  namespace: microservices-infrastructure
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: redis-cluster-init
        image: redis:7-alpine
        command:
        - /bin/sh
        - -c
        - |
          set -e
          echo "Waiting for Redis pods to be ready..."
          sleep 60
          
          REDIS_PASSWORD=$(cat /etc/redis-secret/redis-password)
          
          # Get pod IPs
          REDIS_IPS=""
          for i in $(seq 0 5); do
            IP=$(nslookup redis-cluster-$i.redis-cluster.microservices-infrastructure.svc.cluster.local | grep Address | tail -1 | awk '{print $2}')
            REDIS_IPS="$REDIS_IPS $IP:6379"
          done
          
          echo "Creating Redis cluster with nodes: $REDIS_IPS"
          redis-cli -a $REDIS_PASSWORD --cluster create $REDIS_IPS --cluster-replicas 1 --cluster-yes
          
          echo "Redis cluster created successfully"
        volumeMounts:
        - name: redis-secret
          mountPath: /etc/redis-secret
          readOnly: true
      volumes:
      - name: redis-secret
        secret:
          secretName: redis-secret

---
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster-access
  namespace: microservices-infrastructure
  labels:
    app: redis-cluster
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    name: client
  selector:
    app: redis-cluster
