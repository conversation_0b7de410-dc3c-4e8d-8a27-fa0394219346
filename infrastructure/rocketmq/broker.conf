# Apache RocketMQ Broker Configuration
# Configuration for RocketMQ broker in cluster mode

# Cluster Configuration
clusterName=DefaultCluster
brokerName=broker-a
brokerId=0
deleteWhen=04
fileReservedTime=48
brokerRole=ASYNC_MASTER
flushDiskType=ASYNC_FLUSH

# Network Configuration
listenPort=10911
namesrvAddr=rocketmq-nameserver-1:9876;rocketmq-nameserver-2:9876
brokerIP1=***********

# Store Configuration
storePathRootDir=/home/<USER>/store
storePathCommitLog=/home/<USER>/store/commitlog
storePathConsumeQueue=/home/<USER>/store/consumequeue
storePathIndex=/home/<USER>/store/index
storeCheckpoint=/home/<USER>/store/checkpoint
abortFile=/home/<USER>/store/abort

# CommitLog Configuration
mapedFileSizeCommitLog=1073741824
mapedFileSizeConsumeQueue=300000
destroyMapedFileIntervalForcibly=120000
redeleteHangedFileInterval=120000
diskMaxUsedSpaceRatio=88
storePathRootDir=/home/<USER>/store

# Flush Configuration
flushCommitLogLeastPages=4
flushConsumeQueueLeastPages=2
flushCommitLogThoroughInterval=10000
flushConsumeQueueThoroughInterval=60000

# Message Configuration
maxMessageSize=65536
checkCRCOnRecover=true
flushCommitLogTimed=false
flushIntervalCommitLog=500
flushIntervalConsumeQueue=1000

# Consumer Configuration
maxTransferBytesOnMessageInMemory=262144
maxTransferCountOnMessageInMemory=32
maxTransferBytesOnMessageInDisk=65536
maxTransferCountOnMessageInDisk=8

# Producer Configuration
sendMessageThreadPoolNums=128
pullMessageThreadPoolNums=128
queryMessageThreadPoolNums=8
adminBrokerThreadPoolNums=16
clientManagerThreadPoolNums=32
consumerManagerThreadPoolNums=32

# High Availability Configuration
haMasterAddress=
haListenPort=10912
haSendHeartbeatInterval=5000
haHousekeepingInterval=20000
haTransferBatchSize=32768

# Slave Configuration
accessMessageInMemoryMaxRatio=40
messageIndexEnable=true
maxHashSlotNum=5000000
maxIndexNum=20000000
maxMsgsNumBatch=64
messageIndexSafe=false

# Transaction Configuration
transactionTimeOut=6000
transactionCheckMax=15
transactionCheckInterval=60000

# ACL Configuration
aclEnable=false

# Auto Create Topic/Subscription
autoCreateTopicEnable=true
autoCreateSubscriptionGroup=true
defaultTopicQueueNums=8

# Message Track
traceTopicEnable=false
msgTraceTopicName=RMQ_SYS_TRACE_TOPIC

# Performance Tuning
sendThreadPoolQueueCapacity=10000
pullThreadPoolQueueCapacity=100000
replyThreadPoolQueueCapacity=10000
queryThreadPoolQueueCapacity=20000
clientManagerThreadPoolQueueCapacity=1000000
consumerManagerThreadPoolQueueCapacity=1000000

# Timing Configuration
waitTimeMillsInSendQueue=200
waitTimeMillsInPullQueue=5000
waitTimeMillsInHeartbeatQueue=31000
waitTimeMillsInTransactionQueue=3000

# Filter Configuration
enablePropertyFilter=false
compressedRegister=false

# Metrics Configuration
enableActingMaster=false

# Commercial Configuration
commercialEnable=true
commercialTimerCount=1
commercialTransCount=1
commercialBigCount=1
commercialBaseCount=1

# Other Configuration
regionId=DefaultRegion
registerBrokerTimeoutMills=6000
slaveReadEnable=false
disableConsumeIfConsumerReadSlowly=false
consumerFallbehindThreshold=17179869184
brokerFastFailureEnable=true
waitTimeMillsInTransactionQueue=3000
