# Nacos Cluster Node Configuration
# Each line represents a Nacos server node in the cluster
# Format: ip:port

# Production Cluster Nodes
nacos-1:8848
nacos-2:8848
nacos-3:8848

# For Docker Compose (local development)
# ***********:8848
# ***********:8848
# ***********:8848

# For Kubernetes (production)
# nacos-0.nacos-headless.default.svc.cluster.local:8848
# nacos-1.nacos-headless.default.svc.cluster.local:8848
# nacos-2.nacos-headless.default.svc.cluster.local:8848
