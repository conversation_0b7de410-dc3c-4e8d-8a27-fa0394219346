# Nacos Cluster Configuration
# Configuration for Nacos service discovery and configuration management cluster

# Server Configuration
server.servlet.contextPath=/nacos
server.port=8848
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.pattern=%h %l %u %t "%r" %s %b %D %{User-Agent}i %{Request-Source}i
server.tomcat.basedir=

# Spring Configuration
spring.datasource.platform=mysql
spring.sql.init.separator=;

# Database Configuration
db.num=1
db.url.0=**********************************************************************************************************************************************************************
db.user.0=nacos
db.password.0=${NACOS_DB_PASSWORD:nacos}
db.pool.config.connectionTimeout=30000
db.pool.config.validationTimeout=10000
db.pool.config.maximumPoolSize=20

# Cluster Configuration
nacos.cluster.conf=/home/<USER>/conf/cluster.conf
nacos.member.list=${NACOS_MEMBER_LIST:}

# Authentication Configuration
nacos.core.auth.enabled=true
nacos.core.auth.system.type=nacos
nacos.core.auth.plugin.nacos.token.secret.key=${NACOS_AUTH_TOKEN:SecretKey012345678901234567890123456789012345678901234567890123456789}
nacos.core.auth.plugin.nacos.token.expire.seconds=18000
nacos.core.auth.default.token.secret.key=${NACOS_AUTH_TOKEN:SecretKey012345678901234567890123456789012345678901234567890123456789}

# Metrics Configuration
management.endpoints.web.exposure.include=*
management.metrics.export.elastic.enabled=false
management.metrics.export.influx.enabled=false

# Logging Configuration
nacos.logs.path=/home/<USER>/logs
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.rotate=true
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd

# JVM Configuration
nacos.standalone=false

# Config Module Configuration
nacos.config.push.maxRetryTime=50
nacos.config.retry.time=3000
nacos.config.cache.enabled=true

# Naming Module Configuration
nacos.naming.distro.taskDispatchThreadCount=10
nacos.naming.distro.taskDispatchPeriod=200
nacos.naming.distro.batchSyncKeyCount=1000
nacos.naming.distro.initDataRatio=0.9
nacos.naming.distro.syncRetryDelay=5000
nacos.naming.data.warmup=true
nacos.naming.expireInstance=true

# CMDB Configuration
nacos.cmdb.dumpTaskInterval=3600
nacos.cmdb.eventTaskInterval=10
nacos.cmdb.labelTaskInterval=300
nacos.cmdb.loadDataAtStart=false

# Metrics Configuration
nacos.metrics.monitor.topPath=/home/<USER>
nacos.metrics.monitor.defaultPath=/home/<USER>/logs
nacos.metrics.monitor.logPath=/home/<USER>/logs

# Core Configuration
nacos.core.snowflake.worker-id=${NACOS_WORKER_ID:1}

# Security Configuration
nacos.security.ignore.urls=/,/error,/**/*.css,/**/*.js,/**/*.html,/**/*.map,/**/*.svg,/**/*.png,/**/*.ico,/console-ui/public/**,/v1/auth/**,/v1/console/health/**,/actuator/**,/v1/console/server/**

# Console Configuration
nacos.console.ui.enabled=true

# Health Check Configuration
nacos.health.checkThreadCount=20
nacos.health.checkTaskTimeout=2000

# Istio Configuration
nacos.istio.mcp.server.enabled=false
