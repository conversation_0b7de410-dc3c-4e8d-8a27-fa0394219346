# APISIX Routes Configuration
# Defines routing rules for microservices

routes:
  # User Service Routes
  - id: user-service-routes
    name: "User Service API"
    uri: "/api/v1/users/*"
    methods: ["GET", "POST", "PUT", "DELETE"]
    upstream:
      name: user-service-upstream
      type: roundrobin
      scheme: http
      discovery_type: nacos
      service_name: user-service
      nodes:
        - host: user-service
          port: 8081
          weight: 1
    plugins:
      limit-req:
        rate: 100
        burst: 200
        rejected_code: 429
        nodelay: false
      limit-count:
        count: 1000
        time_window: 60
        rejected_code: 429
        policy: local
      api-breaker:
        break_response_code: 502
        max_breaker_sec: 300
        unhealthy:
          http_statuses: [500, 502, 503]
          failures: 5
        healthy:
          http_statuses: [200, 201]
          successes: 3
      prometheus:
        prefer_name: true

  # Order Service Routes
  - id: order-service-routes
    name: "Order Service API"
    uri: "/api/v1/orders/*"
    methods: ["GET", "POST", "PUT", "DELETE"]
    upstream:
      name: order-service-upstream
      type: roundrobin
      scheme: http
      discovery_type: nacos
      service_name: order-service
      nodes:
        - host: order-service
          port: 8082
          weight: 1
    plugins:
      limit-req:
        rate: 200
        burst: 400
        rejected_code: 429
        nodelay: false
      limit-count:
        count: 2000
        time_window: 60
        rejected_code: 429
        policy: local
      api-breaker:
        break_response_code: 502
        max_breaker_sec: 300
        unhealthy:
          http_statuses: [500, 502, 503]
          failures: 5
        healthy:
          http_statuses: [200, 201]
          successes: 3
      prometheus:
        prefer_name: true

  # Product Service Routes
  - id: product-service-routes
    name: "Product Service API"
    uri: "/api/v1/products/*"
    methods: ["GET", "POST", "PUT", "DELETE"]
    upstream:
      name: product-service-upstream
      type: roundrobin
      scheme: http
      discovery_type: nacos
      service_name: product-service
      nodes:
        - host: product-service
          port: 8083
          weight: 1
    plugins:
      limit-req:
        rate: 500
        burst: 1000
        rejected_code: 429
        nodelay: false
      limit-count:
        count: 5000
        time_window: 60
        rejected_code: 429
        policy: local
      api-breaker:
        break_response_code: 502
        max_breaker_sec: 300
        unhealthy:
          http_statuses: [500, 502, 503]
          failures: 5
        healthy:
          http_statuses: [200, 201]
          successes: 3
      prometheus:
        prefer_name: true

  # Notification Service Routes
  - id: notification-service-routes
    name: "Notification Service API"
    uri: "/api/v1/notifications/*"
    methods: ["GET", "POST", "PUT", "DELETE"]
    upstream:
      name: notification-service-upstream
      type: roundrobin
      scheme: http
      discovery_type: nacos
      service_name: notification-service
      nodes:
        - host: notification-service
          port: 8084
          weight: 1
    plugins:
      limit-req:
        rate: 300
        burst: 600
        rejected_code: 429
        nodelay: false
      limit-count:
        count: 3000
        time_window: 60
        rejected_code: 429
        policy: local
      api-breaker:
        break_response_code: 502
        max_breaker_sec: 300
        unhealthy:
          http_statuses: [500, 502, 503]
          failures: 5
        healthy:
          http_statuses: [200, 201]
          successes: 3
      prometheus:
        prefer_name: true

# Global Plugins
global_plugins:
  cors:
    allow_origins: "*"
    allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
    allow_headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
    expose_headers: "Content-Length,Content-Range"
    max_age: 1728000
    allow_credential: false

  real-ip:
    source: "http_x_forwarded_for"
    trusted_addresses:
      - "*********/24"
      - "**********/12"
      - "***********/16"
      - "10.0.0.0/8"

  request-id:
    include_in_response: true
    algorithm: "uuid"

# Upstreams Configuration
upstreams:
  - id: user-service-upstream
    name: user-service-upstream
    type: roundrobin
    scheme: http
    discovery_type: nacos
    service_name: user-service
    hash_on: vars
    checks:
      active:
        type: http
        http_path: "/actuator/health"
        healthy:
          interval: 10
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 10
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 5
          timeouts: 3

  - id: order-service-upstream
    name: order-service-upstream
    type: roundrobin
    scheme: http
    discovery_type: nacos
    service_name: order-service
    hash_on: vars
    checks:
      active:
        type: http
        http_path: "/actuator/health"
        healthy:
          interval: 10
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 10
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 5
          timeouts: 3

  - id: product-service-upstream
    name: product-service-upstream
    type: roundrobin
    scheme: http
    discovery_type: nacos
    service_name: product-service
    hash_on: vars
    checks:
      active:
        type: http
        http_path: "/actuator/health"
        healthy:
          interval: 10
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 10
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 5
          timeouts: 3

  - id: notification-service-upstream
    name: notification-service-upstream
    type: roundrobin
    scheme: http
    discovery_type: nacos
    service_name: notification-service
    hash_on: vars
    checks:
      active:
        type: http
        http_path: "/actuator/health"
        healthy:
          interval: 10
          http_statuses: [200, 302]
          successes: 2
        unhealthy:
          interval: 10
          http_statuses: [429, 404, 500, 501, 502, 503, 504, 505]
          http_failures: 5
          timeouts: 3
