# APISIX Gateway Configuration
# Main configuration file for Apache APISIX API Gateway

apisix:
  node_listen: 9080
  enable_ipv6: false
  
  allow_admin:
    - *********/24
    - **********/12
    - ***********/16
    - 10.0.0.0/8
  
  admin_key:
    - name: "admin"
      key: edd1c9f034335f136f87ad84b625c8f1
      role: admin
    - name: "viewer"
      key: 4054f7cf07e344346cd3f287985e76a2
      role: viewer

  enable_control: true
  control:
    ip: "0.0.0.0"
    port: 9092

etcd:
  host:
    - "http://etcd-1:2379"
    - "http://etcd-2:2379"
    - "http://etcd-3:2379"
  prefix: "/apisix"
  timeout: 30

nginx_config:
  error_log: "/usr/local/apisix/logs/error.log"
  error_log_level: "warn"
  worker_processes: "auto"
  enable_reuseport: true
  
  worker_rlimit_nofile: 20480
  worker_connections: 10620
  
  http:
    access_log: "/usr/local/apisix/logs/access.log"
    keepalive_timeout: 60s
    client_header_timeout: 60s
    client_body_timeout: 60s
    send_timeout: 10s
    
    underscores_in_headers: "on"
    real_ip_header: "X-Real-IP"
    real_ip_from:
      - 127.0.0.1
      - 'unix:'
    
    lua_shared_dict:
      prometheus-metrics: 15m
      plugin-limit-req: 10m
      plugin-limit-count: 10m
      plugin-limit-conn: 10m
      internal-status: 1m
      upstream-healthcheck: 1m
      worker-events: 10m
      lrucache-lock: 10m
      balancer-ewma: 10m
      balancer-ewma-locks: 10m
      balancer-ewma-last-touched-at: 10m
      plugin-limit-count-redis-cluster-slot-lock: 1m
      tracing_buffer: 10m
      plugin-api-breaker: 10m
      etcd-cluster-health-check: 10m
      discovery: 1m
      jwks: 1m
      introspection: 10m
      access-tokens: 1m

plugins:
  - real-ip
  - ai
  - client-control
  - proxy-control
  - request-id
  - zipkin
  - ext-plugin-pre-req
  - fault-injection
  - mocking
  - serverless-pre-function
  - cors
  - ip-restriction
  - ua-restriction
  - referer-restriction
  - csrf
  - uri-blocker
  - request-validation
  - openid-connect
  - cas-auth
  - authz-casbin
  - authz-casdoor
  - wolf-rbac
  - ldap-auth
  - hmac-auth
  - basic-auth
  - jwt-auth
  - key-auth
  - consumer-restriction
  - serverless-post-function
  - ext-plugin-post-req
  - limit-req
  - limit-count
  - limit-conn
  - degraphql
  - proxy-cache
  - body-transformer
  - proxy-mirror
  - proxy-rewrite
  - workflow
  - api-breaker
  - limit-count
  - prometheus
  - node-status
  - redirect
  - response-rewrite
  - server-info
  - traffic-split
  - gzip
  - real-ip
  - sls-logger
  - tcp-logger
  - kafka-logger
  - rocketmq-logger
  - syslog
  - udp-logger
  - file-logger
  - loggly
  - datadog
  - loki-logger
  - http-logger
  - splunk-hec-logging
  - skywalking-logger
  - google-cloud-logging
  - elasticsearch-logger
  - tencent-cloud-cls
  - grpc-transcode
  - grpc-web
  - public-api
  - dubbo-proxy
  - kafka-proxy

stream_plugins:
  - mqtt-proxy
  - ip-restriction
  - limit-conn
  - prometheus

plugin_attr:
  prometheus:
    export_addr:
      ip: "0.0.0.0"
      port: 9091
  
  skywalking:
    service_name: apisix
    service_instance_name: "APISIX Instance"
    endpoint_addr: http://skywalking:12800
  
  opentelemetry:
    trace_id_source: x-request-id
    resource:
      service.name: APISIX
    collector:
      address: jaeger:14268
      request_timeout: 3
      request_headers:
        foo: bar

deployment:
  role: traditional
  role_traditional:
    config_provider: etcd
  
  admin:
    admin_key_required: true
    enable_admin_cors: true
    admin_listen:
      ip: 0.0.0.0
      port: 9180
    
    https_admin: false
    admin_api_mtls:
      admin_ssl_cert: ""
      admin_ssl_cert_key: ""
      admin_ssl_ca_cert: ""
    
    admin_api_version: v3
