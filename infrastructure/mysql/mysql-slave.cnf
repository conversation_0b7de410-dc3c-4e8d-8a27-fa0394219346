# MySQL Slave Configuration
# Configuration for MySQL slave servers in replication setup

[mysqld]
# Server Identification (change for each slave: 2, 3, 4, etc.)
server-id = 2
bind-address = 0.0.0.0
port = 3306

# Read-Only Configuration
read-only = 1
super-read-only = 1

# Relay Log Configuration
relay-log = relay-bin
relay-log-index = relay-bin.index
relay-log-info-file = relay-log.info
relay-log-recovery = ON
relay-log-purge = ON

# GTID Configuration
gtid-mode = ON
enforce-gtid-consistency = ON
log-slave-updates = ON

# Replication Settings
slave-parallel-type = LOGICAL_CLOCK
slave-parallel-workers = 8
slave-preserve-commit-order = ON
slave-pending-jobs-size-max = 128M
slave-checkpoint-period = 300
slave-checkpoint-group = 512

# Binary Logging (for cascading replication if needed)
log-bin = mysql-bin
binlog-format = ROW
expire-logs-days = 3

# InnoDB Configuration
innodb_flush_log_at_trx_commit = 2
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 8
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 4000
innodb_read_io_threads = 8
innodb_write_io_threads = 8

# Performance Tuning
max_connections = 1000
max_connect_errors = 100000
table_open_cache = 4000
table_definition_cache = 2000
thread_cache_size = 100
query_cache_type = 0
query_cache_size = 0

# Memory Settings
key_buffer_size = 256M
max_allowed_packet = 64M
thread_stack = 256K
thread_cache_size = 100

# Temporary Tables
tmp_table_size = 256M
max_heap_table_size = 256M

# Logging
general_log = 0
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 0

# Error Logging
log_error = /var/log/mysql/error.log
log_warnings = 2

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# Time Zone
default-time-zone = '+00:00'

# SQL Mode
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Security
local-infile = 0
skip-show-database

# Slave Configuration Commands (to be executed manually)
# CHANGE MASTER TO
#   MASTER_HOST='mysql-master',
#   MASTER_USER='replication',
#   MASTER_PASSWORD='replication_password',
#   MASTER_AUTO_POSITION=1;
# START SLAVE;

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock
