# MySQL Master Configuration
# Configuration for MySQL master server in replication setup

[mysqld]
# Server Identification
server-id = 1
bind-address = 0.0.0.0
port = 3306

# Binary Logging for Replication
log-bin = mysql-bin
binlog-format = ROW
binlog-do-db = user_service
binlog-do-db = order_service
binlog-do-db = product_service
binlog-do-db = notification_service

# GTID Configuration
gtid-mode = ON
enforce-gtid-consistency = ON
log-slave-updates = ON

# Replication Settings
sync_binlog = 1
binlog-cache-size = 1M
max-binlog-cache-size = 2G
max-binlog-size = 1G
expire-logs-days = 7
binlog-rows-query-log-events = ON

# InnoDB Configuration
innodb_flush_log_at_trx_commit = 1
innodb_sync_binlog = 1
innodb_buffer_pool_size = 2G
innodb_buffer_pool_instances = 8
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 4000

# Performance Tuning
max_connections = 1000
max_connect_errors = 100000
table_open_cache = 4000
table_definition_cache = 2000
thread_cache_size = 100
query_cache_type = 0
query_cache_size = 0

# Memory Settings
key_buffer_size = 256M
max_allowed_packet = 64M
thread_stack = 256K
thread_cache_size = 100
myisam_recover_options = BACKUP

# Temporary Tables
tmp_table_size = 256M
max_heap_table_size = 256M

# Logging
general_log = 0
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
log_slow_admin_statements = 1

# Error Logging
log_error = /var/log/mysql/error.log
log_warnings = 2

# Character Set
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init-connect = 'SET NAMES utf8mb4'

# Time Zone
default-time-zone = '+00:00'

# SQL Mode
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Security
local-infile = 0
skip-show-database

# Replication User (to be created manually)
# CREATE USER 'replication'@'%' IDENTIFIED BY 'replication_password';
# GRANT REPLICATION SLAVE ON *.* TO 'replication'@'%';

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = 3306
socket = /var/run/mysqld/mysqld.sock
