# Redis Cluster Configuration
# This configuration is for a 6-node Redis cluster (3 masters + 3 replicas)

# Network Configuration
bind 0.0.0.0
port 7000
protected-mode no

# Cluster Configuration
cluster-enabled yes
cluster-config-file nodes.conf
cluster-node-timeout 15000
cluster-announce-ip ${REDIS_ANNOUNCE_IP}
cluster-announce-port ${REDIS_ANNOUNCE_PORT}
cluster-announce-bus-port ${REDIS_ANNOUNCE_BUS_PORT}

# Persistence Configuration
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# RDB Configuration
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb

# Memory Management
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Logging
loglevel notice
logfile /var/log/redis/redis-server.log
syslog-enabled yes
syslog-ident redis

# Security
requirepass ${REDIS_PASSWORD}
masterauth ${REDIS_PASSWORD}

# Performance Tuning
tcp-keepalive 300
timeout 0
tcp-backlog 511
databases 16

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Hash Configuration
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List Configuration
list-max-ziplist-size -2
list-compress-depth 0

# Set Configuration
set-max-intset-entries 512

# Sorted Set Configuration
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog Configuration
hll-sparse-max-bytes 3000

# Streams Configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active Rehashing
activerehashing yes

# Client Query Buffer
client-query-buffer-limit 1gb

# Protocol Max Bulk Length
proto-max-bulk-len 512mb

# Lazy Freeing
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# Threading
io-threads 4
io-threads-do-reads yes

# TLS Configuration (if needed)
# tls-port 6380
# tls-cert-file /etc/redis/tls/redis.crt
# tls-key-file /etc/redis/tls/redis.key
# tls-ca-cert-file /etc/redis/tls/ca.crt
