# Apache Pulsar Cluster Configuration
# Configuration for Pulsar broker in cluster mode

# Cluster Configuration
clusterName=pulsar-cluster
brokerServiceUrl=pulsar://pulsar-broker:6650
brokerServiceUrlTls=pulsar+ssl://pulsar-broker:6651
webServiceUrl=http://pulsar-broker:8080
webServiceUrlTls=https://pulsar-broker:8443

# ZooKeeper Configuration
zookeeperServers=zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181
configurationStoreServers=zookeeper-1:2181,zookeeper-2:2181,zookeeper-3:2181

# BookKeeper Configuration
managedLedgerDefaultEnsembleSize=3
managedLedgerDefaultWriteQuorum=2
managedLedgerDefaultAckQuorum=2
managedLedgerCacheSizeMB=1024
managedLedgerCacheEvictionWatermark=0.9
managedLedgerDefaultRetentionTimeInMinutes=10080
managedLedgerDefaultRetentionSizeInMB=10240

# Broker Configuration
brokerDeleteInactiveTopicsEnabled=true
brokerDeleteInactiveTopicsFrequencySeconds=60
messageExpiryCheckIntervalInMinutes=5
subscriptionExpiryCheckIntervalInMinutes=5
brokerServiceCompactionMonitorIntervalInSeconds=60

# Load Balancer Configuration
loadBalancerEnabled=true
loadBalancerPlacementStrategy=weightedRandomSelection
loadBalancerReportUpdateThresholdPercentage=10
loadBalancerReportUpdateMaxIntervalMinutes=15
loadBalancerHostUsageCheckIntervalMinutes=1
loadBalancerSheddingIntervalMinutes=1
loadBalancerSheddingGracePeriodMinutes=30

# Namespace Configuration
defaultRetentionTimeInMinutes=10080
defaultRetentionSizeInMB=1000
backlogQuotaDefaultLimitGB=10
backlogQuotaDefaultRetentionPolicy=producer_exception

# Message Configuration
maxMessageSize=5242880
maxPublishRatePerTopicInMessages=10000
maxPublishRatePerTopicInBytes=**********
maxConsumerReconnectSinceLastDisconnectMs=300000
maxUnackedMessagesPerConsumer=50000
maxUnackedMessagesPerSubscription=200000

# Authentication Configuration
authenticationEnabled=false
authorizationEnabled=false
superUserRoles=

# TLS Configuration
tlsEnabled=false
tlsCertificateFilePath=
tlsKeyFilePath=
tlsTrustCertsFilePath=

# Function Worker Configuration
functionsWorkerEnabled=false

# Transaction Configuration
transactionCoordinatorEnabled=true
transactionMetadataStoreProviderClassName=org.apache.pulsar.transaction.coordinator.impl.MLTransactionMetadataStoreProvider

# Schema Registry Configuration
isSchemaValidationEnforced=false
schemaRegistryStorageClassName=org.apache.pulsar.broker.service.schema.BookkeeperSchemaStorageFactory

# Compaction Configuration
brokerServiceCompactionThresholdInBytes=104857600
brokerServiceCompactionMonitorIntervalInSeconds=60

# Replication Configuration
replicationConnectionsPerBroker=16
replicationProducerQueueSize=1000

# Metrics Configuration
exposeTopicLevelMetricsInPrometheus=true
exposeConsumerLevelMetricsInPrometheus=false
exposeProducerLevelMetricsInPrometheus=false
exposePublisherStats=true
statsUpdateFrequencyInSecs=60

# Log Configuration
managedLedgerMaxEntriesPerLedger=50000
managedLedgerMinLedgerRolloverTimeMinutes=10
managedLedgerMaxLedgerRolloverTimeMinutes=240

# Offload Configuration
managedLedgerOffloadDriver=null
managedLedgerOffloadMaxThreads=2

# Geo-replication Configuration
replicationMetricsEnabled=true
replicationConnectionsPerBroker=16
replicationProducerQueueSize=1000

# Subscription Configuration
subscriptionKeySharedEnable=true
subscriptionKeySharedUseConsistentHashing=true
subscriptionReplicationCheckBacklogSize=1000

# Dispatcher Configuration
dispatcherMaxReadBatchSize=100
dispatcherMaxReadSizeBytes=5242880
dispatcherMinReadBatchSize=1
dispatcherMaxRoundRobinBatchSize=20

# Memory Configuration
directMemoryUsageThreshold=0.95
maxDirectMemoryUsage=0

# HTTP Configuration
httpMaxRequestSize=65536
httpRequestsLimitEnabled=false
httpRequestsMaxPerSecond=100.0

# WebSocket Configuration
webSocketServiceEnabled=false

# Proxy Configuration
proxyRoles=

# Deprecated Configuration
# These are kept for backward compatibility
managedLedgerCursorBackloggedThreshold=1000
