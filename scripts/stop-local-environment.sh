#!/bin/bash

# Enterprise Microservices Framework - Stop Local Development Environment
# This script stops all services and cleans up the local development environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to stop services
stop_services() {
    print_status "Stopping application services..."
    
    if [ -f docker/docker-compose.services.yml ]; then
        docker-compose -f docker/docker-compose.services.yml down
        print_success "Application services stopped"
    else
        print_warning "Services compose file not found"
    fi
}

# Function to stop infrastructure
stop_infrastructure() {
    print_status "Stopping infrastructure services..."
    
    if [ -f docker/docker-compose.infrastructure.yml ]; then
        docker-compose -f docker/docker-compose.infrastructure.yml down
        print_success "Infrastructure services stopped"
    else
        print_warning "Infrastructure compose file not found"
    fi
}

# Function to clean up volumes (optional)
cleanup_volumes() {
    if [ "${1:-}" = "--clean-data" ]; then
        print_warning "Cleaning up all data volumes..."
        read -p "This will delete all data. Are you sure? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose -f docker/docker-compose.infrastructure.yml down -v
            docker-compose -f docker/docker-compose.services.yml down -v
            print_success "Data volumes cleaned up"
        else
            print_status "Data volumes preserved"
        fi
    fi
}

# Function to clean up networks
cleanup_networks() {
    print_status "Cleaning up networks..."
    
    docker network rm microservices-network 2>/dev/null || true
    
    print_success "Networks cleaned up"
}

# Function to show remaining containers
show_remaining() {
    print_status "Checking for remaining containers..."
    
    remaining=$(docker ps -a --filter "name=mysql-master\|redis-\|nacos-\|pulsar-\|apisix\|etcd\|zookeeper-\|bookkeeper-\|prometheus\|grafana\|sentinel-dashboard" --format "table {{.Names}}\t{{.Status}}" | tail -n +2)
    
    if [ -n "$remaining" ]; then
        print_warning "Remaining containers:"
        echo "$remaining"
        echo ""
        print_status "To remove all containers: docker rm -f \$(docker ps -aq)"
    else
        print_success "No remaining containers found"
    fi
}

# Main execution
main() {
    echo "🛑 Stopping Enterprise Microservices Framework Local Environment"
    echo "================================================================"
    
    stop_services
    stop_infrastructure
    cleanup_volumes "$@"
    cleanup_networks
    show_remaining
    
    echo ""
    print_success "Local development environment stopped"
    echo ""
    echo "To start again: ./scripts/start-local-environment.sh"
    echo "To clean all data: ./scripts/stop-local-environment.sh --clean-data"
}

# Handle script arguments
case "${1:-}" in
    "services")
        stop_services
        ;;
    "infrastructure")
        stop_infrastructure
        ;;
    "--clean-data")
        main --clean-data
        ;;
    *)
        main "$@"
        ;;
esac
