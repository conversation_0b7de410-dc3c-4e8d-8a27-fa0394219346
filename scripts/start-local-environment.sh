#!/bin/bash

# Enterprise Microservices Framework - Local Development Environment Setup
# This script sets up the complete local development environment using Docker Compose

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    print_success "Docker is running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    print_success "Docker Compose is available"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p logs/mysql
    mkdir -p logs/redis
    mkdir -p logs/nacos
    mkdir -p logs/pulsar
    mkdir -p logs/apisix
    mkdir -p data/mysql
    mkdir -p data/redis
    mkdir -p data/nacos
    mkdir -p data/pulsar
    
    print_success "Directories created"
}

# Function to set up environment variables
setup_environment() {
    print_status "Setting up environment variables..."
    
    if [ ! -f .env ]; then
        cat > .env << EOF
# Database Configuration
MYSQL_ROOT_PASSWORD=rootpassword
MYSQL_PASSWORD=password
MYSQL_USER=microservices
MYSQL_DATABASE=microservices

# Redis Configuration
REDIS_PASSWORD=redispassword

# Nacos Configuration
NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789
NACOS_DB_PASSWORD=nacos

# Application Configuration
JWT_SECRET=mySecretKey
REGION=us-east-1
ZONE=us-east-1a

# Monitoring Configuration
GRAFANA_ADMIN_PASSWORD=admin
EOF
        print_success "Environment file created"
    else
        print_warning "Environment file already exists"
    fi
}

# Function to start infrastructure services
start_infrastructure() {
    print_status "Starting infrastructure services..."
    
    # Create network first
    docker network create microservices-network --driver bridge --subnet=**********/16 2>/dev/null || true
    
    # Start infrastructure services
    docker-compose -f docker/docker-compose.infrastructure.yml up -d
    
    print_status "Waiting for infrastructure services to be ready..."
    
    # Wait for MySQL
    print_status "Waiting for MySQL master to be ready..."
    until docker exec mysql-master mysqladmin ping -h localhost --silent; do
        sleep 2
    done
    print_success "MySQL master is ready"
    
    # Wait for Redis cluster
    print_status "Waiting for Redis cluster to be ready..."
    sleep 30
    print_success "Redis cluster should be ready"
    
    print_success "Infrastructure services started"
}

# Function to start application services
start_services() {
    print_status "Starting application services..."
    
    docker-compose -f docker/docker-compose.services.yml up -d
    
    print_status "Waiting for application services to be ready..."
    
    # Wait for Nacos
    print_status "Waiting for Nacos to be ready..."
    until curl -f http://localhost:8848/nacos/actuator/health > /dev/null 2>&1; do
        sleep 5
    done
    print_success "Nacos is ready"
    
    # Wait for APISIX
    print_status "Waiting for APISIX to be ready..."
    until curl -f http://localhost:9080/apisix/status > /dev/null 2>&1; do
        sleep 5
    done
    print_success "APISIX is ready"
    
    print_success "Application services started"
}

# Function to initialize databases
initialize_databases() {
    print_status "Initializing databases..."
    
    # Create databases for each service
    docker exec mysql-master mysql -uroot -prootpassword -e "
        CREATE DATABASE IF NOT EXISTS user_service;
        CREATE DATABASE IF NOT EXISTS order_service;
        CREATE DATABASE IF NOT EXISTS product_service;
        CREATE DATABASE IF NOT EXISTS notification_service;
        CREATE DATABASE IF NOT EXISTS nacos;
        
        CREATE USER IF NOT EXISTS 'nacos'@'%' IDENTIFIED BY 'nacos';
        GRANT ALL PRIVILEGES ON nacos.* TO 'nacos'@'%';
        
        CREATE USER IF NOT EXISTS 'replication'@'%' IDENTIFIED BY 'replication_password';
        GRANT REPLICATION SLAVE ON *.* TO 'replication'@'%';
        
        FLUSH PRIVILEGES;
    "
    
    print_success "Databases initialized"
}

# Function to configure APISIX routes
configure_apisix() {
    print_status "Configuring APISIX routes..."
    
    # Wait a bit more for APISIX to be fully ready
    sleep 10
    
    # Configure routes using APISIX Admin API
    # This would typically be done through the admin API or configuration files
    print_warning "APISIX route configuration should be done manually or through CI/CD"
    
    print_success "APISIX configuration completed"
}

# Function to display service URLs
display_urls() {
    print_success "Local development environment is ready!"
    echo ""
    echo "Service URLs:"
    echo "============="
    echo "🌐 APISIX Gateway:        http://localhost:9080"
    echo "🔧 APISIX Admin:          http://localhost:9180"
    echo "🎯 Nacos Console:         http://localhost:8848/nacos (nacos/nacos)"
    echo "🛡️  Sentinel Dashboard:    http://localhost:8858"
    echo "📊 Prometheus:            http://localhost:9090"
    echo "📈 Grafana:               http://localhost:3000 (admin/admin)"
    echo "🗄️  MySQL Master:          localhost:3306"
    echo "🗄️  MySQL Slave:           localhost:3307"
    echo "🔴 Redis Cluster:         localhost:7001-7006"
    echo "📨 RocketMQ NameServer:   localhost:9876"
    echo "📨 RocketMQ Console:      http://localhost:8082"
    echo ""
    echo "Service Endpoints (through APISIX):"
    echo "==================================="
    echo "👤 User Service:          http://localhost:9080/api/v1/users"
    echo "🛒 Order Service:         http://localhost:9080/api/v1/orders"
    echo "📦 Product Service:       http://localhost:9080/api/v1/products"
    echo "📧 Notification Service:  http://localhost:9080/api/v1/notifications"
    echo ""
    echo "To stop all services: ./scripts/stop-local-environment.sh"
    echo "To view logs: docker-compose -f docker/docker-compose.infrastructure.yml logs -f [service-name]"
}

# Function to check service health
check_health() {
    print_status "Checking service health..."
    
    services=(
        "mysql-master:3306"
        "redis-1:7001"
        "nacos-1:8848"
        "apisix:9080"
        "prometheus:9090"
        "grafana:3000"
    )
    
    for service in "${services[@]}"; do
        IFS=':' read -r name port <<< "$service"
        if nc -z localhost "$port" 2>/dev/null; then
            print_success "$name is healthy"
        else
            print_warning "$name is not responding on port $port"
        fi
    done
}

# Main execution
main() {
    echo "🚀 Starting Enterprise Microservices Framework Local Environment"
    echo "================================================================"
    
    check_docker
    check_docker_compose
    create_directories
    setup_environment
    start_infrastructure
    initialize_databases
    start_services
    configure_apisix
    
    echo ""
    print_status "Performing health checks..."
    sleep 10
    check_health
    
    echo ""
    display_urls
}

# Handle script arguments
case "${1:-}" in
    "infrastructure")
        check_docker
        check_docker_compose
        create_directories
        setup_environment
        start_infrastructure
        initialize_databases
        ;;
    "services")
        start_services
        configure_apisix
        ;;
    "health")
        check_health
        ;;
    *)
        main
        ;;
esac
